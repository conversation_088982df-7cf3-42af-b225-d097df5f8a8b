#!/bin/bash

# Production Deployment Script for Darvi Group Registration

echo "🚀 Starting Production Deployment..."

# Check if environment variables are set
if [ ! -f ".env.production" ]; then
    echo "❌ Error: .env.production file not found!"
    echo "📝 Please copy .env.production.example to .env.production and fill in your values"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build for production
echo "🔨 Building for production..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    
    # Deploy to Netlify
    echo "🌐 Deploying to Netlify..."
    netlify deploy --prod --dir=build
    
    if [ $? -eq 0 ]; then
        echo "🎉 Deployment successful!"
        echo ""
        echo "📋 Post-deployment checklist:"
        echo "  1. Test the registration form"
        echo "  2. Complete a test payment"
        echo "  3. Verify Google Sheets submission"
        echo "  4. Check receipt auto-download"
        echo "  5. Test on mobile devices"
        echo ""
        echo "🔗 Your site should be live at your Netlify URL"
    else
        echo "❌ Deployment failed!"
        exit 1
    fi
else
    echo "❌ Build failed!"
    exit 1
fi
