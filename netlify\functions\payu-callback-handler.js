/**
 * PayU Payment Callback Handler
 * Processes PayU payment responses and redirects to appropriate React pages
 * Handles success, failure, and cancel scenarios with proper data validation
 */

// Import utilities
const { getPayUConfig } = require('./utils/payuConfig');
const { verifyPayUHash } = require('./utils/hashGenerator');
const { validatePayUResponse, sanitizePayUResponse } = require('./utils/payuValidator');
const { logPaymentResponse, logError, debugLog } = require('./utils/logger');

// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Content-Type': 'application/json'
};

/**
 * Main handler function
 */
exports.handler = async (event, context) => {
  // Add comprehensive logging for debugging
  debugLog('PayU callback handler invoked', {
    method: event.httpMethod,
    path: event.path,
    headers: Object.keys(event.headers || {}),
    hasBody: !!event.body,
    bodyLength: event.body?.length || 0,
    queryParams: event.queryStringParameters
  });

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'CORS preflight handled' })
    };
  }

  try {
    // Get PayU configuration
    const config = getPayUConfig();

    // Parse payment data from both POST body and query parameters
    let paymentData = {};

    // Handle POST data (form submission from PayU)
    if (event.httpMethod === 'POST' && event.body) {
      debugLog('Processing POST data', {
        bodyLength: event.body.length,
        contentType: event.headers['content-type'] || 'unknown'
      });

      try {
        // Try parsing as JSON first
        paymentData = JSON.parse(event.body);
        debugLog('Parsed as JSON', { keys: Object.keys(paymentData) });
      } catch (e) {
        // If not JSON, parse as form data
        const params = new URLSearchParams(event.body);
        paymentData = Object.fromEntries(params.entries());
        debugLog('Parsed as form data', { keys: Object.keys(paymentData) });
      }
    }

    // Handle GET parameters (URL redirect from PayU)
    if (event.queryStringParameters) {
      paymentData = { ...paymentData, ...event.queryStringParameters };
      debugLog('Added query parameters', { totalKeys: Object.keys(paymentData).length });
    }

    // Log the parsed payment data (sanitized)
    debugLog('Payment data received', {
      txnid: paymentData.txnid,
      status: paymentData.status,
      amount: paymentData.amount,
      mihpayid: paymentData.mihpayid,
      hasHash: !!paymentData.hash,
      totalFields: Object.keys(paymentData).length
    });

    // Validate required payment data
    const validation = validatePayUResponse(paymentData);
    if (!validation.isValid) {
      logError('Invalid PayU response data', {
        errors: validation.errors,
        receivedData: paymentData
      });
      
      return redirectToFailure('Invalid payment response data', paymentData.txnid);
    }

    // Sanitize the response data
    const sanitized = sanitizePayUResponse(paymentData);
    
    // Extract key payment information
    const {
      status,
      txnid,
      amount,
      mihpayid,
      mode,
      firstname,
      lastname,
      email,
      phone,
      productinfo,
      hash,
      error,
      error_Message
    } = sanitized;

    // Determine callback type based on status
    const callbackType = determineCallbackType(status, error);

    // Handle different callback types
    switch (callbackType) {
      case 'success':
        return await handleSuccessCallback(sanitized, config);
      
      case 'failure':
        return await handleFailureCallback(sanitized, config);
      
      case 'pending':
        return await handlePendingCallback(sanitized, config);
      
      case 'cancel':
        return await handleCancelCallback(sanitized, config);
      
      default:
        return redirectToFailure('Unknown payment status', txnid);
    }

  } catch (error) {
    logError('PayU callback handler error', {
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      event: {
        method: event.httpMethod,
        path: event.path,
        body: event.body?.substring(0, 500) // Log first 500 chars only
      }
    });

    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: 'Payment processing failed. Please contact support.',
        timestamp: new Date().toISOString()
      })
    };
  }
};

/**
 * Determine callback type based on status and error
 */
function determineCallbackType(status, error) {
  if (!status) return 'unknown';
  
  const statusLower = status.toLowerCase();
  
  if (statusLower === 'success') return 'success';
  if (statusLower === 'failure' || error) return 'failure';
  if (statusLower === 'pending') return 'pending';
  if (statusLower === 'cancel' || statusLower === 'cancelled') return 'cancel';
  
  return 'unknown';
}

/**
 * Handle successful payment callback
 */
async function handleSuccessCallback(paymentData, config) {
  const { txnid, amount, mihpayid, mode, firstname, email, phone, hash } = paymentData;

  try {
    // Verify payment hash for security
    let hashVerified = false;
    let hashVerification = null;

    if (hash && hash.trim() !== '') {
      hashVerification = verifyPayUHash(paymentData, hash, config.salt);
      hashVerified = hashVerification.isValid;
    }

    // For successful payments, we'll proceed even if hash verification fails
    // This is because PayU sometimes has issues with hash consistency
    // But we'll log the verification status for monitoring
    if (!hashVerified && hash) {
      logError('Hash verification failed but proceeding with successful payment', {
        txnid,
        expectedHash: hashVerification?.expectedHash?.substring(0, 20) + '...',
        receivedHash: hash?.substring(0, 20) + '...',
        message: hashVerification?.message,
        note: 'Proceeding with payment as PayU indicated success'
      });
    }

    // Log successful payment
    logPaymentResponse(paymentData, hashVerified);

    // Create success URL with clean parameters
    const successUrl = createSuccessRedirectUrl({
      status: 'success',
      txnid,
      amount,
      mihpayid,
      mode,
      firstname,
      email,
      phone,
      verified: 'true',
      hashVerified: hashVerified ? 'true' : 'false',
      timestamp: new Date().toISOString()
    });

    // Log the redirect URL for debugging
    debugLog('Redirecting to success page', {
      successUrl,
      txnid,
      amount,
      mihpayid
    });

    // Redirect to success page
    return {
      statusCode: 302,
      headers: {
        ...corsHeaders,
        'Location': successUrl
      },
      body: ''
    };

  } catch (error) {
    logError('Error processing successful payment', {
      txnid,
      error: error.message
    });
    
    return redirectToFailure('Payment processing error', txnid);
  }
}

/**
 * Handle failed payment callback
 */
async function handleFailureCallback(paymentData, config) {
  const { txnid, error, error_Message } = paymentData;
  
  // Log failed payment
  logPaymentResponse(paymentData, false);
  
  const failureUrl = createFailureRedirectUrl({
    status: 'failure',
    txnid,
    error: error || 'Payment failed',
    message: error_Message || 'Payment could not be processed',
    timestamp: new Date().toISOString()
  });

  // Redirect to failure page

  return {
    statusCode: 302,
    headers: {
      ...corsHeaders,
      'Location': failureUrl
    },
    body: ''
  };
}

/**
 * Handle pending payment callback
 */
async function handlePendingCallback(paymentData, config) {
  const { txnid } = paymentData;
  
  // Log pending payment
  logPaymentResponse(paymentData, null);
  
  const pendingUrl = createPendingRedirectUrl({
    status: 'pending',
    txnid,
    message: 'Payment is being processed',
    timestamp: new Date().toISOString()
  });

  return {
    statusCode: 302,
    headers: {
      ...corsHeaders,
      'Location': pendingUrl
    },
    body: ''
  };
}

/**
 * Handle cancelled payment callback
 */
async function handleCancelCallback(paymentData, config) {
  const { txnid } = paymentData;
  
  const cancelUrl = createFailureRedirectUrl({
    status: 'cancelled',
    txnid,
    error: 'Payment cancelled',
    message: 'Payment was cancelled by user',
    timestamp: new Date().toISOString()
  });

  return {
    statusCode: 302,
    headers: {
      ...corsHeaders,
      'Location': cancelUrl
    },
    body: ''
  };
}

/**
 * Create success redirect URL - redirect to payment-success page
 */
function createSuccessRedirectUrl(params) {
  const baseUrl = process.env.NODE_ENV === 'production'
    ? 'https://darvigroup.in'
    : 'http://localhost:3000';

  // Create parameters for payment-success page
  const successParams = new URLSearchParams({
    txnid: params.txnid || '',
    mihpayid: params.mihpayid || '',
    amount: params.amount || '',
    mode: params.mode || '',
    firstname: params.firstname || '',
    email: params.email || '',
    phone: params.phone || '',
    status: 'success',
    verified: params.verified || 'true',
    hashVerified: params.hashVerified || 'false',
    timestamp: params.timestamp || new Date().toISOString()
  });

  return `${baseUrl}/payment-success?${successParams.toString()}`;
}

/**
 * Create failure redirect URL - redirect to form page with payment=failure
 */
function createFailureRedirectUrl(params) {
  const baseUrl = process.env.NODE_ENV === 'production'
    ? 'https://darvigroup.in'
    : 'http://localhost:3000';

  // Create parameters for form page with payment=failure
  const formParams = new URLSearchParams({
    payment: 'failure',
    txnid: params.txnid || '',
    status: params.status || 'failed',
    error: params.error || '',
    message: params.message || 'Payment failed',
    timestamp: params.timestamp || new Date().toISOString()
  });

  return `${baseUrl}/form?${formParams.toString()}`;
}

/**
 * Create pending redirect URL
 */
function createPendingRedirectUrl(params) {
  const baseUrl = process.env.NODE_ENV === 'production' 
    ? 'https://darvigroup.in' 
    : 'http://localhost:3000';
  
  const searchParams = new URLSearchParams(params);
  return `${baseUrl}/payment-success?${searchParams.toString()}`;
}

/**
 * Generic failure redirect helper
 */
function redirectToFailure(message, txnid = null) {
  const failureUrl = createFailureRedirectUrl({
    status: 'failure',
    txnid: txnid || 'unknown',
    error: 'Processing Error',
    message,
    timestamp: new Date().toISOString()
  });

  return {
    statusCode: 302,
    headers: {
      ...corsHeaders,
      'Location': failureUrl
    },
    body: ''
  };
}
