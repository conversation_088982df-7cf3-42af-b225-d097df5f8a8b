import { ReceiptData, createReceiptData, validateReceiptD<PERSON>, createReceiptSummary } from '../services/receiptService';

/**
 * Extract receipt data from payment success information
 * This function now uses the centralized receiptService for consistency
 */
export function extractReceiptData(
  paymentData: any,
  formData: any,
  transactionDetails: any
): ReceiptData {
  // Use the centralized receipt service to create receipt data
  // This ensures consistent client ID generation and data handling
  return createReceiptData(paymentData, formData, transactionDetails);
}

// Re-export functions from receiptService for backward compatibility
export { validateReceiptData, createReceiptSummary } from '../services/receiptService';

const receiptDataExtractor = {
  extractReceiptData,
  validateReceiptData,
  createReceiptSummary
};

export default receiptDataExtractor;
