# Darvi Group CMS Setup Guide

## Overview

This project now includes a comprehensive Content Management System (CMS) that allows you to edit all content, settings, and configurations without touching the code. The CMS is built using Netlify CMS and provides a user-friendly interface for managing your website.

## 🚀 Quick Start

### Accessing the CMS

1. **Production**: Visit `https://darvigroup.in/admin/`
2. **Development**: Visit `http://localhost:8888/admin/`

### First Time Setup

1. Enable Netlify Identity on your Netlify site
2. Invite users to access the CMS
3. Users will receive an email to set up their password
4. Login and start editing content!

## 📁 CMS Structure

### 1. **Home Page Content**
- Hero slides with images and multilingual text
- About section content
- Contact information
- Gallery images
- Team member profiles
- Company values

### 2. **Page Content**
- About page content
- Services page content
- Pricing information
- Contact page details
- FAQ content
- Legal pages (Terms, Privacy, Refund)

### 3. **Layout Components**
- Header/Navigation menu items
- Footer content and links
- SEO meta data for all pages

### 4. **Website Settings**
- Site information (title, description, logo)
- Contact details (phone, email, address)
- Social media links
- GST number and business details

### 5. **Payment Configuration**
- Registration fee amounts
- GST percentage
- Payment gateway settings
- Enabled payment methods

### 6. **Receipt Settings**
- Receipt header information
- Footer messages
- Styling options (colors, fonts)
- Company branding

### 7. **UI Content**
- Button text and labels
- Success/error messages
- Form field labels and placeholders
- Validation messages

### 8. **Feature Toggles**
- Enable/disable payment features
- Form functionality toggles
- UI feature flags
- Email/SMS notifications

### 9. **Email Templates**
- Registration confirmation emails
- Payment confirmation emails
- Welcome emails
- Custom email content

### 10. **Analytics & Tracking**
- Google Analytics configuration
- Facebook Pixel settings
- Custom event tracking

## 🛠️ Technical Implementation

### CMS Files Structure

```
src/
├── config/
│   ├── cms-settings.json          # Site settings
│   ├── payment-settings.json      # Payment configuration
│   ├── receipt-settings.json      # Receipt settings
│   ├── form-settings.json         # Form configuration
│   ├── email-templates.json       # Email templates
│   ├── features.json              # Feature flags
│   └── analytics.json             # Analytics settings
├── content/
│   ├── home/                      # Home page content
│   ├── about/                     # About page content
│   ├── services/                  # Services content
│   ├── contact/                   # Contact page content
│   ├── pricing/                   # Pricing content
│   ├── legal/                     # Legal pages
│   ├── layout/                    # Layout components
│   ├── seo/                       # SEO meta data
│   └── ui/                        # UI text content
├── hooks/
│   └── useCMSContent.ts           # CMS content hooks
├── contexts/
│   └── CMSContext.tsx             # CMS context provider
└── components/
    └── CMSAdmin.tsx               # Admin interface
```

### Using CMS Content in Components

```typescript
import { useCMS } from '../contexts/CMSContext';

const MyComponent = () => {
  const { cmsSettings, paymentSettings, uiContent } = useCMS();
  
  return (
    <div>
      <h1>{cmsSettings?.site.title}</h1>
      <p>Registration Fee: {paymentSettings?.registration.totalAmount}</p>
      <button>{uiContent?.buttons.primary.register}</button>
    </div>
  );
};
```

## 🎨 Customization Options

### 1. **Visual Branding**
- Upload company logo and favicon
- Set brand colors for receipts
- Customize font families and sizes
- Configure social media links

### 2. **Content Management**
- Edit all text content in multiple languages
- Manage images and media files
- Update contact information
- Modify pricing and payment details

### 3. **Feature Control**
- Enable/disable payment gateway
- Toggle email notifications
- Control form validation
- Manage UI animations

### 4. **Payment Configuration**
- Set registration fees and GST
- Configure payment methods
- Customize receipt format
- Manage transaction settings

## 🔧 Advanced Configuration

### Adding New Content Types

1. Add new collection to `public/admin/config.yml`
2. Create corresponding JSON file in `src/content/`
3. Update TypeScript interfaces in `useCMSContent.ts`
4. Add to CMS context and hooks

### Custom Fields

```yaml
# Example: Adding a new field to services
- label: "Service Duration"
  name: "duration"
  widget: "number"
  default: 60
  hint: "Duration in minutes"
```

### Multilingual Support

The CMS supports multiple languages (English, Kannada, Hindi):

```yaml
- label: "Title"
  name: "title"
  widget: "object"
  fields:
    - { label: "English", name: "en", widget: "string" }
    - { label: "Kannada", name: "kn", widget: "string" }
    - { label: "Hindi", name: "hi", widget: "string" }
```

## 📱 Mobile-Friendly Admin

The CMS admin interface is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- All modern browsers

## 🔒 Security & Access Control

### User Roles
- **Admin**: Full access to all content and settings
- **Editor**: Access to content but not settings
- **Contributor**: Limited access to specific sections

### Access Management
1. Go to Netlify Dashboard → Identity
2. Invite users by email
3. Set user roles and permissions
4. Users receive setup email

## 🚀 Deployment

### Automatic Deployment
- Changes made in CMS automatically trigger builds
- Content updates are live within minutes
- No developer intervention required

### Manual Deployment
```bash
# Build and deploy
npm run build
netlify deploy --prod
```

## 📊 Content Backup

### Automatic Backups
- All content is stored in Git repository
- Every change creates a commit
- Full version history available

### Manual Backup
```bash
# Export all content
git clone https://github.com/aimaniahub/darvi-registration.git
```

## 🆘 Troubleshooting

### Common Issues

1. **Can't access CMS**
   - Check Netlify Identity is enabled
   - Verify user has been invited
   - Clear browser cache

2. **Changes not appearing**
   - Wait for build to complete (2-3 minutes)
   - Check build logs in Netlify
   - Verify content was saved

3. **Images not loading**
   - Check image file size (max 10MB)
   - Verify image format (JPG, PNG, WebP)
   - Ensure proper file permissions

### Support

For technical support:
- Email: <EMAIL>
- Phone: +91 99868 90777
- Create issue on GitHub repository

## 🎯 Best Practices

1. **Content Guidelines**
   - Keep text concise and clear
   - Use high-quality images
   - Test on mobile devices
   - Maintain consistent branding

2. **SEO Optimization**
   - Update meta descriptions
   - Use descriptive image alt text
   - Keep URLs clean and readable
   - Monitor page load speeds

3. **Regular Maintenance**
   - Review content monthly
   - Update contact information
   - Check for broken links
   - Monitor analytics data

## 🔄 Updates & Maintenance

The CMS system is designed to be self-maintaining. However, periodic updates may include:
- New content types
- Enhanced features
- Security improvements
- Performance optimizations

All updates are backward compatible and won't affect existing content.
