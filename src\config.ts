/**
 * Production application configuration for Darvi Group
 * Version: 1.0.0
 * Environment: Production Ready
 */
const config = {
  // Google Apps Script URL for form submissions and email sending
  googleScriptUrl: process.env.REACT_APP_GOOGLE_SCRIPT_URL || 'https://script.google.com/macros/s/AKfycbxRSDhiwDLn8ZjKChVZ5lNV_oiv846wR5l7jD08of6C6O5tw2KncyxngMcqDm19r-jWtA/exec',

  // Email configuration
  contactEmail: process.env.REACT_APP_CONTACT_EMAIL || '<EMAIL>',

  // Application configuration
  app: {
    name: 'Darvi Group',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    domain: process.env.NODE_ENV === 'production' ? 'darvigroup.in' : 'localhost:3000'
  },

  // PayU Hosted Checkout Configuration
  payu: {
    // API URLs - Dynamic based on environment and test mode
    // The backend will determine the correct PayU URL based on credentials and test mode
    apiUrl: process.env.NODE_ENV === 'production'
      ? 'https://secure.payu.in/_payment'  // Backend will switch to test URL if needed
      : 'https://test.payu.in/_payment',

    // Netlify Functions API URLs
    functionsUrl: process.env.NODE_ENV === 'production'
      ? 'https://darvigroup.in/.netlify/functions'
      : 'http://localhost:8888/.netlify/functions',

    // Payment amount in INR (₹4,725)
    paymentAmount: 4725.00,

    // Payment amount display (for UI)
    paymentAmountDisplay: '₹4,725',

    // Company name for payment
    companyName: 'Darvi Group',

    // PayU callback URLs - use Netlify routing for preprocessing
    successUrl: process.env.NODE_ENV === 'production'
      ? 'https://darvigroup.in/payu/success'
      : 'http://localhost:3000/payu/success',

    failureUrl: process.env.NODE_ENV === 'production'
      ? 'https://darvigroup.in/payu/failure'
      : 'http://localhost:3000/payu/failure',

    // Cancel URL for user-initiated cancellations
    cancelUrl: process.env.NODE_ENV === 'production'
      ? 'https://darvigroup.in/payu/cancel'
      : 'http://localhost:3000/payu/cancel',

    // Environment configuration
    environment: process.env.NODE_ENV === 'production' ? 'production' : 'test',

    // API endpoints for Netlify Functions
    endpoints: {
      initiate: '/payu-initiate',
      verify: '/payu-verify',
      callback: '/payu-callback',
      webhook: '/payu-webhook',
      transactionVerify: '/payu-transaction-verify'
    },

    // Timeout configuration
    timeout: {
      payment: 300000, // 5 minutes
      statusCheck: 10000, // 10 seconds
      polling: 120000 // 2 minutes
    },

    // PayU Hosted Checkout Features
    hostedCheckout: {
      // Enable Native UI for brand consistency
      enableNativeUI: true,

      // Custom branding (configured in PayU dashboard)
      branding: {
        merchantLogo: 'https://darvigroup.in/darvi-logo.png',
        primaryColor: '#1976d2', // Darvi Group primary color
        backgroundColor: '#ffffff',
        fontFamily: 'Roboto, Arial, sans-serif'
      },

      // Language support
      language: 'en', // Default: English (supports: en, hi, te, ta, kn, ml, bn, gu, mr, pa)

      // Payment modes configuration
      paymentModes: {
        // Enable all major payment modes
        cards: true,           // Credit/Debit Cards
        netBanking: true,      // Net Banking
        upi: true,             // UPI Payments
        wallets: true,         // Digital Wallets
        emi: true,             // EMI Options
        cardless_emi: true,    // Cardless EMI

        // UPI specific settings
        upiSettings: {
          enableIntent: true,   // Enable UPI intent for mobile
          enableQR: true,       // Enable UPI QR code
          enableCollect: true   // Enable UPI collect
        }
      },

      // Security features
      security: {
        enableOTPAuth: true,        // Enable OTP authentication
        enableNativeOTP: true,      // Native OTP flow
        enableCardStorage: true,    // Allow card storage with consent
        enableFraudCheck: true,     // Enable fraud detection
        enableRiskAnalysis: true    // Enable risk analysis
      },

      // User experience features
      userExperience: {
        enableRecommendations: true,  // Personalized payment recommendations
        enableOffers: true,           // Show available offers
        enableSaveCard: true,         // Allow save card option
        enableExpressCheckout: true,  // Express checkout for returning users
        enableMobileOptimization: true, // Mobile-optimized flow
        enableProgressIndicator: true  // Show payment progress
      },

      // Checkout customization
      customization: {
        showMerchantName: true,
        showOrderSummary: true,
        showPaymentMethods: true,
        enableBackButton: true,
        enableRetryPayment: true,
        autoSelectPaymentMode: false // Let user choose payment mode
      }
    }
  },

  // Security configuration
  security: {
    enforceHTTPS: process.env.NODE_ENV === 'production',
    allowedOrigins: [
      'https://darvigroup.in',
      'https://www.darvigroup.in'
    ],

    enableCSRFProtection: process.env.NODE_ENV === 'production',
    validatePaymentAmount: true
  },

  // Validation configuration
  validation: {
    minPaymentAmount: 1, // PayU minimum: ₹1
    maxPaymentAmount: 100000, // ₹100,000 maximum for safety
    allowedMobilePattern: /^[6-9]\d{9}$/, // Indian mobile number pattern
    requiredFields: [
      'customerName',
      'customerMobile',
      'address',
      'city',
      'district',
      'taluk',
      'landArea',
      'soilType'
    ]
  }
};

export default config;
