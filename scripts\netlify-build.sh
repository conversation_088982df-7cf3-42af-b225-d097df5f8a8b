#!/bin/bash

# Netlify build script for Darvi Group
# This script ensures proper build environment and handles TypeScript compilation

echo "🔧 Starting Netlify build process..."

# Set environment variables
export CI=false
export SKIP_PREFLIGHT_CHECK=true
export ESLINT_NO_DEV_ERRORS=true
export GENERATE_SOURCEMAP=false
export NODE_ENV=production

# Check Node.js version
echo "📋 Node.js version: $(node --version)"
echo "📋 NPM version: $(npm --version)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install --legacy-peer-deps

# Check if src/App.tsx exists
if [ ! -f "src/App.tsx" ]; then
    echo "❌ Error: src/App.tsx not found!"
    exit 1
fi

# Check if src/index.tsx exists
if [ ! -f "src/index.tsx" ]; then
    echo "❌ Error: src/index.tsx not found!"
    exit 1
fi

echo "✅ Source files verified"

# Build the React app
echo "🏗️  Building React application..."
npm run build:netlify

# Check if build was successful
if [ ! -d "build" ]; then
    echo "❌ Build failed - build directory not created"
    exit 1
fi

echo "✅ React build completed successfully"

# Install function dependencies
echo "📦 Installing function dependencies..."
cd netlify/functions && npm install

echo "🚀 Build process completed successfully!"
