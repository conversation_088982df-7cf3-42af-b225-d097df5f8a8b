/**
 * PayU Health Check Function
 * Provides diagnostic information about PayU configuration
 * Useful for debugging production issues
 */

const { validateEnvironment } = require('./utils/payuConfig');

// CORS headers
const getCORSHeaders = (origin) => {
  const allowedOrigins = process.env.NODE_ENV === 'production' 
    ? ['https://darvigroup.in', 'https://www.darvigroup.in']
    : ['http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000'];
  
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  return {
    'Access-Control-Allow-Origin': isAllowedOrigin ? origin : allowedOrigins[0],
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block'
  };
};

exports.handler = async (event, context) => {
  const corsHeaders = getCORSHeaders(event.headers.origin);

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  // Only allow GET requests
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: 'Method not allowed'
      })
    };
  }

  try {
    // Check environment configuration
    const envValidation = validateEnvironment();
    
    // Prepare health check response
    const healthData = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'unknown',
      netlifyContext: context.clientContext ? 'available' : 'not available',
      
      // Configuration status
      configuration: {
        isValid: envValidation.isValid,
        errors: envValidation.errors,
        
        // Safe configuration info (no sensitive data)
        ...(envValidation.isValid && {
          merchantId: envValidation.config.merchantId,
          testMode: envValidation.config.testMode,
          paymentUrl: envValidation.config.paymentUrl,
          successUrl: envValidation.config.successUrl,
          failureUrl: envValidation.config.failureUrl,
          hasCredentials: {
            merchantKey: !!envValidation.config.merchantKey,
            salt: !!envValidation.config.salt,
            clientId: !!envValidation.config.clientId,
            clientSecret: !!envValidation.config.clientSecret
          }
        })
      },
      
      // Environment variables status (without values)
      environmentVariables: {
        NODE_ENV: !!process.env.NODE_ENV,
        PAYU_MERCHANT_KEY: !!process.env.PAYU_MERCHANT_KEY,
        PAYU_MERCHANT_ID: !!process.env.PAYU_MERCHANT_ID,
        PAYU_SALT_256: !!process.env.PAYU_SALT_256,
        PAYU_SALT_32: !!process.env.PAYU_SALT_32,
        PAYU_CLIENT_ID: !!process.env.PAYU_CLIENT_ID,
        PAYU_CLIENT_SECRET: !!process.env.PAYU_CLIENT_SECRET,
        PAYU_TEST_MODE: !!process.env.PAYU_TEST_MODE,
        PAYU_BASE_URL: !!process.env.PAYU_BASE_URL,
        PAYU_PAYMENT_URL: !!process.env.PAYU_PAYMENT_URL
      }
    };

    // Determine overall health status
    const isHealthy = envValidation.isValid;
    const statusCode = isHealthy ? 200 : 500;

    return {
      statusCode: statusCode,
      headers: corsHeaders,
      body: JSON.stringify({
        success: isHealthy,
        message: isHealthy ? 'PayU service is healthy' : 'PayU service has configuration issues',
        data: healthData
      })
    };

  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: 'Health check failed',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      })
    };
  }
};
