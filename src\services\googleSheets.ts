import config from '../config';

interface FormData {
  [key: string]: any;
}

class GoogleSheetsService {
  private scriptUrl: string;
  private isConfigured: boolean;

  constructor() {
    this.scriptUrl = config.googleScriptUrl;
    this.isConfigured = !!this.scriptUrl;
  }

  async submitForm(data: FormData): Promise<{ success: boolean; message: string; clientId?: string }> {
    if (!this.isConfigured) {
      return {
        success: false,
        message: 'Google Sheets integration is not configured. Please contact support.',
      };
    }

    try {
      // Validate required data before submission
      if (!data.customerName && !data.name) {
        throw new Error('Missing required form data (customerName or name)');
      }

      if (!data.customerMobile && !data.mobile) {
        throw new Error('Missing required form data (customerMobile or mobile)');
      }

      // Prepare the data payload
      const payload = {
        ...data,
        // Ensure all required fields are present
        name: data.customerName || data.name,
        mobile: data.customerMobile || data.mobile,
        email: data.email || data.customerEmail,
        timestamp: data.timestamp || new Date().toISOString(),
        clientId: data.clientId || `DG${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
        paymentCompleted: data.paymentCompleted || false,
        paymentAmount: data.paymentAmount || '₹4,799',
        paymentMethod: data.paymentMethod || 'PayU'
      };

      // Submit to Google Apps Script using no-cors mode
      await fetch(this.scriptUrl, {
        method: 'POST',
        mode: 'no-cors',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      // With no-cors mode, we can't read the response but assume success if no error thrown
      return {
        success: true,
        message: 'Data submitted successfully to Google Sheets',
        clientId: payload.clientId
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to submit form to Google Sheets. Please try again.',
      };
    }
  }

  // Production-ready service - debug functions removed
}

const googleSheetsService = new GoogleSheetsService();

// Debug functions removed for production

export default googleSheetsService;