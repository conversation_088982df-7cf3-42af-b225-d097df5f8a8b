/**
 * PayU Payment Modal Component
 * Handles payment initiation and user interface
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Divider,
  Paper,
  Grid,
  Chip
} from '@mui/material';
import {
  Payment as PaymentIcon,
  Security as SecurityIcon
} from '@mui/icons-material';

import payuService from '../../services/payuService';
import payuValidator from '../../utils/payuValidator';
import config from '../../config';
import { useLanguage } from '../../contexts/LanguageContext';

interface PayUModalProps {
  open: boolean;
  onClose: () => void;
  onPaymentSuccess: (txnid: string) => void;
  onPaymentFailure: (error: string) => void;
  customerName: string;
  customerMobile: string;
  formData: any;
}

const PayUModal: React.FC<PayUModalProps> = ({
  open,
  onClose,
  onPaymentSuccess,
  onPaymentFailure,
  customerName,
  customerMobile,
  formData
}) => {
  const { language } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Translations
  const translations = {
    en: {
      title: 'Complete Payment',
      subtitle: 'Secure payment powered by PayU',
      amount: 'Amount to Pay',
      customerInfo: 'Customer Information',
      name: 'Name',
      mobile: 'Mobile',
      email: 'Email',
      paymentDetails: 'Payment Details',
      service: 'Service',
      serviceName: 'Darvi Group Farmer Registration',
      securityNote: 'Your payment is secured with 256-bit SSL encryption',
      payNow: 'Pay Now',
      processing: 'Processing Payment...',
      cancel: 'Cancel',
      validationError: 'Please fix the following errors:',
      paymentError: 'Payment Error'
    },
    hi: {
      title: 'भुगतान पूरा करें',
      subtitle: 'PayU द्वारा संचालित सुरक्षित भुगतान',
      amount: 'भुगतान राशि',
      customerInfo: 'ग्राहक जानकारी',
      name: 'नाम',
      mobile: 'मोबाइल',
      email: 'ईमेल',
      paymentDetails: 'भुगतान विवरण',
      service: 'सेवा',
      serviceName: 'दर्वी ग्रुप किसान पंजीकरण',
      securityNote: 'आपका भुगतान 256-बिट SSL एन्क्रिप्शन के साथ सुरक्षित है',
      payNow: 'अभी भुगतान करें',
      processing: 'भुगतान प्रक्रिया...',
      cancel: 'रद्द करें',
      validationError: 'कृपया निम्नलिखित त्रुटियों को ठीक करें:',
      paymentError: 'भुगतान त्रुटि'
    },
    kn: {
      title: 'ಪಾವತಿ ಪೂರ್ಣಗೊಳಿಸಿ',
      subtitle: 'PayU ನಿಂದ ಚಾಲಿತ ಸುರಕ್ಷಿತ ಪಾವತಿ',
      amount: 'ಪಾವತಿಸಬೇಕಾದ ಮೊತ್ತ',
      customerInfo: 'ಗ್ರಾಹಕ ಮಾಹಿತಿ',
      name: 'ಹೆಸರು',
      mobile: 'ಮೊಬೈಲ್',
      email: 'ಇಮೇಲ್',
      paymentDetails: 'ಪಾವತಿ ವಿವರಗಳು',
      service: 'ಸೇವೆ',
      serviceName: 'ದರ್ವಿ ಗುಂಪು ರೈತ ನೋಂದಣಿ',
      securityNote: 'ನಿಮ್ಮ ಪಾವತಿಯು 256-ಬಿಟ್ SSL ಎನ್‌ಕ್ರಿಪ್ಶನ್‌ನೊಂದಿಗೆ ಸುರಕ್ಷಿತವಾಗಿದೆ',
      payNow: 'ಈಗ ಪಾವತಿಸಿ',
      processing: 'ಪಾವತಿ ಪ್ರಕ್ರಿಯೆ...',
      cancel: 'ರದ್ದುಮಾಡಿ',
      validationError: 'ದಯವಿಟ್ಟು ಈ ಕೆಳಗಿನ ದೋಷಗಳನ್ನು ಸರಿಪಡಿಸಿ:',
      paymentError: 'ಪಾವತಿ ದೋಷ'
    }
  };

  const t = translations[language as keyof typeof translations];



  // Validate form data on component mount
  useEffect(() => {
    if (open && formData) {
      const validation = payuValidator.validateFormData({
        name: customerName,
        email: formData.email || '<EMAIL>',
        mobile: customerMobile,
        address: formData.address,
        city: formData.city,
        pincode: formData.pincode
      });

      if (!validation.isValid) {
        setValidationErrors(validation.errors);
      } else {
        setValidationErrors([]);
      }
    }
  }, [open, formData, customerName, customerMobile]);

  // Validate hosted checkout configuration on mount
  useEffect(() => {
    if (open) {
      const configValidation = payuService.validateHostedCheckoutConfig();
      if (!configValidation.isValid) {
        // Configuration issues detected
      }
    }
  }, [open]);

  const handlePayment = async () => {
    if (validationErrors.length > 0) {
      setError('Please fix validation errors before proceeding');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Sanitize form data
      const sanitizedData = payuValidator.sanitizeFormData({
        name: customerName,
        email: formData.email || '<EMAIL>',
        mobile: customerMobile,
        address: formData.address,
        city: formData.city,
        pincode: formData.pincode,
        landArea: formData.landArea,
        soilType: formData.soilType,
        district: formData.district,
        taluk: formData.taluk
      });

      // Initiate payment
      const response = await payuService.initiatePayment({
        ...formData,
        ...sanitizedData
      });

      if (response.success) {
        // Store transaction details for success page
        localStorage.setItem('payuCheckoutData', JSON.stringify({
          ...formData,
          txnid: response.data?.txnid,
          amount: config.payu.paymentAmount,
          customerName,
          customerMobile
        }));

        // Payment initiated successfully

        // Close modal - the actual payment success will be handled by URL callback
        // when PayU redirects back to the app after successful payment
        onClose();

        // Note: onPaymentSuccess will be called from the URL callback handler
        // in Form.tsx when PayU redirects back with success parameters
      } else {
        const errorMessage = response.message || 'Payment initiation failed. Please try again.';
        // Payment initiation failed

        setError(errorMessage);
        onPaymentFailure(errorMessage);
      }
    } catch (err) {
      // Payment modal error occurred

      const errorMessage = err instanceof Error ? err.message : 'Something went wrong. Please try again later.';
      setError(errorMessage);
      onPaymentFailure(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setError(null);
      setValidationErrors([]);
      onClose();
    }
  };

  const handleCancel = () => {
    if (!loading) {
      setError(null);
      setValidationErrors([]);

      // If user explicitly cancels, treat as failure
      onPaymentFailure('Payment cancelled by user');
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown={loading}
      sx={{
        '& .MuiDialog-paper': {
          margin: { xs: 1, sm: 2 },
          maxHeight: { xs: '95vh', sm: '90vh' },
          borderRadius: { xs: '12px', sm: '16px' }
        }
      }}
    >
      <DialogTitle sx={{
        pb: { xs: 1, sm: 2 },
        px: { xs: 2, sm: 3 }
      }}>
        <Box display="flex" alignItems="center" gap={{ xs: 1, sm: 1.5 }}>
          <PaymentIcon
            color="primary"
            sx={{ fontSize: { xs: '1.5rem', sm: '1.75rem' } }}
          />
          <Box>
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                fontWeight: 600,
                lineHeight: 1.2
              }}
            >
              {t.title}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                fontSize: { xs: '0.8rem', sm: '0.875rem' },
                lineHeight: 1.3,
                mt: 0.5
              }}
            >
              {t.subtitle}
            </Typography>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent sx={{
        px: { xs: 2, sm: 3 },
        py: { xs: 1, sm: 2 }
      }}>
        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert
            severity="error"
            sx={{
              mb: { xs: 1.5, sm: 2 },
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            <Typography
              variant="body2"
              gutterBottom
              sx={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                lineHeight: 1.4
              }}
            >
              {t.validationError}
            </Typography>
            <ul style={{ margin: 0, paddingLeft: '16px' }}>
              {validationErrors.map((error, index) => (
                <li key={index}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontSize: { xs: '0.8rem', sm: '0.875rem' },
                      lineHeight: 1.4
                    }}
                  >
                    {payuValidator.getLocalizedErrorMessage(error, language)}
                  </Typography>
                </li>
              ))}
            </ul>
          </Alert>
        )}

        {/* Payment Error */}
        {error && (
          <Alert
            severity="error"
            sx={{
              mb: { xs: 1.5, sm: 2 },
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            <Typography
              variant="body2"
              sx={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                lineHeight: 1.4
              }}
            >
              {error}
            </Typography>
          </Alert>
        )}

        {/* Payment Amount */}
        <Paper
          elevation={1}
          sx={{
            p: { xs: 1.5, sm: 2 },
            mb: { xs: 1.5, sm: 2 },
            bgcolor: 'primary.light',
            color: 'primary.contrastText',
            borderRadius: { xs: '8px', sm: '12px' }
          }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              fontSize: { xs: '1rem', sm: '1.25rem' },
              fontWeight: 600,
              lineHeight: 1.2
            }}
          >
            {t.amount}
          </Typography>
          <Typography
            variant="h4"
            fontWeight="bold"
            sx={{
              fontSize: { xs: '1.75rem', sm: '2.125rem' },
              lineHeight: 1.1
            }}
          >
            {config.payu.paymentAmountDisplay}
          </Typography>
        </Paper>

        {/* Customer Information */}
        <Box mb={{ xs: 1.5, sm: 2 }}>
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              fontSize: { xs: '1rem', sm: '1.25rem' },
              fontWeight: 600,
              lineHeight: 1.2,
              mb: { xs: 1, sm: 1.5 }
            }}
          >
            {t.customerInfo}
          </Typography>
          <Grid container spacing={{ xs: 1, sm: 2 }}>
            <Grid item xs={12}>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="flex-start"
                sx={{
                  flexDirection: { xs: 'column', sm: 'row' },
                  gap: { xs: 0.5, sm: 0 }
                }}
              >
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    lineHeight: 1.4,
                    minWidth: { sm: '80px' }
                  }}
                >
                  {t.name}:
                </Typography>
                <Typography
                  variant="body2"
                  fontWeight="medium"
                  sx={{
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    lineHeight: 1.4,
                    textAlign: { xs: 'left', sm: 'right' },
                    wordBreak: 'break-word'
                  }}
                >
                  {customerName}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="flex-start"
                sx={{
                  flexDirection: { xs: 'column', sm: 'row' },
                  gap: { xs: 0.5, sm: 0 }
                }}
              >
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    lineHeight: 1.4,
                    minWidth: { sm: '80px' }
                  }}
                >
                  {t.mobile}:
                </Typography>
                <Typography
                  variant="body2"
                  fontWeight="medium"
                  sx={{
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    lineHeight: 1.4,
                    textAlign: { xs: 'left', sm: 'right' }
                  }}
                >
                  {customerMobile}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="flex-start"
                sx={{
                  flexDirection: { xs: 'column', sm: 'row' },
                  gap: { xs: 0.5, sm: 0 }
                }}
              >
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    lineHeight: 1.4,
                    minWidth: { sm: '80px' }
                  }}
                >
                  {t.email}:
                </Typography>
                <Typography
                  variant="body2"
                  fontWeight="medium"
                  sx={{
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    lineHeight: 1.4,
                    textAlign: { xs: 'left', sm: 'right' },
                    wordBreak: 'break-word'
                  }}
                >
                  {formData?.email || '<EMAIL>'}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Payment Details */}
        <Box mb={2}>
          <Typography variant="h6" gutterBottom>
            {t.paymentDetails}
          </Typography>
          <Box display="flex" justifyContent="space-between" mb={1}>
            <Typography variant="body2" color="text.secondary">
              {t.service}:
            </Typography>
            <Typography variant="body2" fontWeight="medium">
              {t.serviceName}
            </Typography>
          </Box>
        </Box>

        {/* Price Breakdown */}
        <Box mb={2}>
          <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
            💰 Price Breakdown
          </Typography>

          {/* Breakdown Table */}
          <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
              {/* Base Amount */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  Registration Fee
                </Typography>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  ₹4,500
                </Typography>
              </Box>

              <Divider sx={{ my: 0.5 }} />

              {/* GST */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  GST (5%)
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  ₹225
                </Typography>
              </Box>

              <Divider sx={{ my: 0.5 }} />

              {/* Total */}
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                bgcolor: 'primary.light',
                p: 1.5,
                borderRadius: 1,
                mt: 1
              }}>
                <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.dark' }}>
                  Total Amount
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 700, color: 'primary.dark' }}>
                  ₹4,725
                </Typography>
              </Box>
            </Box>
          </Paper>

          {/* Calculation Details */}
          <Box mt={2}>
            <Typography variant="caption" color="text.secondary" sx={{ fontStyle: 'italic' }}>
              💡 Calculation: Base (₹4,500) + GST 5% (₹225) = ₹4,725
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Payment Security */}
        <Box mb={2}>
          <Typography variant="h6" gutterBottom sx={{ color: 'success.main', fontWeight: 600 }}>
            🔒 Secure Payment
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
            <Chip label="256-bit SSL Encryption" size="small" color="success" />
            <Chip label="PCI DSS Compliant" size="small" color="success" />
            <Chip label="Bank-Grade Security" size="small" color="success" />
            <Chip label="Fraud Protection" size="small" color="success" />
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
            Your payment is processed securely through PayU's encrypted gateway. All major payment methods accepted.
          </Typography>
        </Box>

        {/* Security Notice */}
        <Paper elevation={1} sx={{ p: 2, bgcolor: 'success.light', display: 'flex', alignItems: 'center' }}>
          <SecurityIcon sx={{ color: 'success.dark', mr: 1 }} />
          <Typography variant="body2" color="success.dark">
            {t.securityNote}
          </Typography>
        </Paper>
      </DialogContent>

      <DialogActions sx={{
        p: { xs: 2, sm: 3 },
        gap: { xs: 1, sm: 2 },
        flexDirection: { xs: 'column', sm: 'row' },
        '& .MuiButton-root': {
          minHeight: { xs: '48px', sm: '44px' }, // Ensure 48px touch target on mobile
          fontSize: { xs: '1rem', sm: '0.875rem' },
          fontWeight: 600
        }
      }}>
        <Button
          onClick={handleCancel}
          disabled={loading}
          variant="outlined"
          sx={{
            minWidth: { xs: 'auto', sm: '100px' },
            order: { xs: 2, sm: 1 },
            width: { xs: '100%', sm: 'auto' }
          }}
        >
          {t.cancel}
        </Button>
        <Button
          onClick={handlePayment}
          disabled={loading || validationErrors.length > 0}
          variant="contained"
          size="large"
          sx={{
            minWidth: { xs: 'auto', sm: '140px' },
            order: { xs: 1, sm: 2 },
            py: { xs: 1.5, sm: 1 },
            width: { xs: '100%', sm: 'auto' }
          }}
        >
          {loading ? (
            <>
              <CircularProgress
                size={20}
                sx={{
                  mr: 1,
                  color: 'inherit'
                }}
              />
              {t.processing}
            </>
          ) : (
            t.payNow
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PayUModal;
