/**
 * Client ID Storage Utility
 * Manages storage and retrieval of client IDs for transactions
 */

export interface ClientIdRecord {
  clientId: string;
  transactionId: string;
  customerName: string;
  customerEmail: string;
  paymentAmount: number;
  createdAt: string;
}

/**
 * Store client ID with transaction details
 */
export function storeClientId(record: ClientIdRecord): void {
  try {
    const existingRecords = getStoredClientIds();
    const updatedRecords = [...existingRecords, record];
    
    // Keep only the last 100 records to prevent localStorage bloat
    const limitedRecords = updatedRecords.slice(-100);
    
    localStorage.setItem('darvi_client_ids', JSON.stringify(limitedRecords));

    // Client ID stored successfully
  } catch (error) {
    // Failed to store client ID
  }
}

/**
 * Retrieve all stored client ID records
 */
export function getStoredClientIds(): ClientIdRecord[] {
  try {
    const stored = localStorage.getItem('darvi_client_ids');
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    // Failed to retrieve client IDs
    return [];
  }
}

/**
 * Find client ID record by transaction ID
 */
export function findClientIdByTransaction(transactionId: string): ClientIdRecord | null {
  try {
    const records = getStoredClientIds();
    return records.find(record => record.transactionId === transactionId) || null;
  } catch (error) {
    // Failed to find client ID
    return null;
  }
}

/**
 * Find client ID record by client ID
 */
export function findClientIdRecord(clientId: string): ClientIdRecord | null {
  try {
    const records = getStoredClientIds();
    return records.find(record => record.clientId === clientId) || null;
  } catch (error) {
    // Failed to find client ID record
    return null;
  }
}

/**
 * Get client ID for a transaction (if exists)
 */
export function getClientIdForTransaction(transactionId: string): string | null {
  const record = findClientIdByTransaction(transactionId);
  return record ? record.clientId : null;
}

/**
 * Clear all stored client ID records
 */
export function clearStoredClientIds(): void {
  try {
    localStorage.removeItem('darvi_client_ids');
    // All client ID records cleared
  } catch (error) {
    // Failed to clear client IDs
  }
}

/**
 * Export client ID records as JSON for backup
 */
export function exportClientIdRecords(): string {
  try {
    const records = getStoredClientIds();
    return JSON.stringify(records, null, 2);
  } catch (error) {
    // Failed to export client ID records
    return '[]';
  }
}

/**
 * Get statistics about stored client IDs
 */
export function getClientIdStats(): {
  totalRecords: number;
  oldestRecord: string | null;
  newestRecord: string | null;
} {
  try {
    const records = getStoredClientIds();
    
    if (records.length === 0) {
      return {
        totalRecords: 0,
        oldestRecord: null,
        newestRecord: null
      };
    }
    
    const sortedByDate = records.sort((a, b) => 
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
    
    return {
      totalRecords: records.length,
      oldestRecord: sortedByDate[0].createdAt,
      newestRecord: sortedByDate[sortedByDate.length - 1].createdAt
    };
  } catch (error) {
    // Failed to get client ID stats
    return {
      totalRecords: 0,
      oldestRecord: null,
      newestRecord: null
    };
  }
}
