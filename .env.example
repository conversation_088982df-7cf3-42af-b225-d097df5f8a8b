# PayU Production Configuration (Replace with your actual production credentials)
# Get these from PayU Dashboard: https://dashboard.payu.in/
PAYU_MERCHANT_KEY=your_production_merchant_key
PAYU_MERCHANT_SALT=your_production_merchant_salt
PAYU_MERCHANT_ID=your_production_merchant_id

# PayU Test Configuration (for development/testing)
PAYU_TEST_KEY=kCDLd6
PAYU_TEST_SALT=ELd0paVbDhHHaDfzpr2GBROiRZxoTj

# PayU API Credentials (Optional - for advanced features)
PAYU_CLIENT_ID=5f0458fc94005072bb08f32fb3427dd099ccb9a0acff22fe74a2e67bde87cf83
PAYU_CLIENT_SECRET=eeb1ce03b4fcba1a9a974a1eae71ec076710bc4f16cd41509acd6dd92aedbc21

# PayU Webhook Configuration (Optional - for enhanced security)
PAYU_WEBHOOK_SECRET=your_webhook_secret_key

# Environment Configuration
NODE_ENV=production
REACT_APP_ENVIRONMENT=production
REACT_APP_ENABLE_SECURITY=true

# Application URLs
REACT_APP_DOMAIN=darvigroup.in
REACT_APP_API_URL=https://darvigroup.in/.netlify/functions

# Google Apps Script Configuration
REACT_APP_GOOGLE_SCRIPT_URL=https://script.google.com/macros/s/AKfycbw7Egddgq6fD2zVBJVdx_8jN_C92sD-8JglW2vuH1KK8fZNzd-Fhjthoee5Si0kuWtuVw/exec
REACT_APP_CONTACT_EMAIL=<EMAIL>

# Security Configuration
REACT_APP_ENABLE_CSRF=true
REACT_APP_ENABLE_RATE_LIMITING=true