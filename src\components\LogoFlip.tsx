import React, { useState, useEffect } from 'react';
import { Box, keyframes } from '@mui/material';

interface LogoFlipProps {
  width?: string | number | Record<string, string>;
  height?: string | number | Record<string, string>;
  className?: string;
}

// Define flip animation keyframes
const flipAnimation = keyframes`
  0% {
    transform: rotateY(0deg);
    opacity: 1;
  }
  45% {
    transform: rotateY(90deg);
    opacity: 0;
  }
  55% {
    transform: rotateY(270deg);
    opacity: 0;
  }
  100% {
    transform: rotateY(360deg);
    opacity: 1;
  }
`;

const LogoFlip: React.FC<LogoFlipProps> = ({
  width = 'auto',
  height = { xs: '40px', sm: '45px' },
  className = ''
}) => {
  const [currentLogo, setCurrentLogo] = useState<number>(0);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);

  // Array of logo paths
  const logos = [
    `${process.env.PUBLIC_URL}/darvi-logo.png`,
    `${process.env.PUBLIC_URL}/darvi-logo-2.png`
  ];

  // Handle image loading error
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    // Using a silent error handling approach for production
    // console.error('Failed to load logo image');
    const target = e.target as HTMLImageElement;
    // If one logo fails, try the other one or fallback to icon
    if (currentLogo === 0) {
      target.src = `${process.env.PUBLIC_URL}/darvi-logo-2.png`;
    } else if (currentLogo === 1) {
      target.src = `${process.env.PUBLIC_URL}/darvi-icon.png`;
    } else {
      target.onerror = null; // Prevent infinite loop
      target.src = `${process.env.PUBLIC_URL}/favicon.ico`;
    }
  };

  useEffect(() => {
    // Set up the interval to switch logos every 5 seconds
    const intervalId = setInterval(() => {
      setIsAnimating(true);

      // After half the animation duration, change the logo
      const timeoutId = setTimeout(() => {
        setCurrentLogo((prev) => (prev === 0 ? 1 : 0));
      }, 500); // Half of the animation duration

      // Reset animation state after animation completes
      const animationEndId = setTimeout(() => {
        setIsAnimating(false);
      }, 1000); // Full animation duration

      return () => {
        clearTimeout(timeoutId);
        clearTimeout(animationEndId);
      };
    }, 5000);

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width,
        height,
        position: 'relative',
      }}
      className={className}
    >
      <Box
        component="img"
        src={logos[currentLogo]}
        alt="Darvi Logo"
        onError={handleImageError}
        sx={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
          animation: isAnimating ? `${flipAnimation} 1s ease-in-out` : 'none',
        }}
      />
    </Box>
  );
};

export default LogoFlip;
