import {
  getCompanyInfo,
  getProductsInfo,
  getProductInfo,
  getPestManagementInfo,
  getDiseaseManagementInfo,
  getSoilHealthInfo,
  getWaterManagementInfo,
  getCropInfo,
  getGeneralResponse
} from '../data/darviContentData';

class SimpleVectorService {
  private static instance: SimpleVectorService;
  private isInitialized: boolean = false;
  private keywordMap: Map<string, string[]> = new Map();

  private constructor() {
    this.initialize();
  }

  public static getInstance(): SimpleVectorService {
    if (!SimpleVectorService.instance) {
      SimpleVectorService.instance = new SimpleVectorService();
    }
    return SimpleVectorService.instance;
  }

  private initialize(): void {
    if (this.isInitialized) {
      return;
    }

    try {
      // Set up keyword mappings for quick lookup
      this.setupKeywordMappings();
      this.isInitialized = true;
    } catch (error) {
      // Silent error handling
    }
  }

  private setupKeywordMappings(): void {
    // Company information keywords
    this.keywordMap.set('company', ['about', 'company', 'darvi', 'who', 'what is', 'mission', 'values', 'contact']);

    // Product keywords
    this.keywordMap.set('products', ['product', 'products', 'price', 'cost', 'buy', 'purchase']);
    this.keywordMap.set('genex', ['genex', 'soil fertility', 'crop yield', 'soil health']);
    this.keywordMap.set('neem', ['neem', 'pest control', 'insect', 'organic farming']);
    this.keywordMap.set('santica', ['santica', 'plant health', 'productivity', 'disease resistance']);

    // Problem keywords
    this.keywordMap.set('pest', ['pest', 'insect', 'bug', 'aphid', 'bollworm', 'whitefly', 'borer']);
    this.keywordMap.set('disease', ['disease', 'fungus', 'bacteria', 'virus', 'infection', 'rot', 'mildew']);
    this.keywordMap.set('soil', ['soil', 'fertility', 'ph', 'nutrient', 'organic matter', 'compaction']);
    this.keywordMap.set('water', ['water', 'irrigation', 'drought', 'waterlogging', 'moisture']);

    // Crop keywords
    this.keywordMap.set('rice', ['rice', 'paddy']);
    this.keywordMap.set('wheat', ['wheat']);
    this.keywordMap.set('sugarcane', ['sugarcane', 'sugar cane']);
    this.keywordMap.set('cotton', ['cotton']);
    this.keywordMap.set('vegetables', ['vegetable', 'tomato', 'potato', 'onion', 'cabbage']);
    this.keywordMap.set('fruit', ['fruit', 'mango', 'apple', 'banana', 'orange']);
    this.keywordMap.set('sandlewood', ['sandlewood', 'sandalwood']);
  }

  public searchContent(query: string): string {
    if (!this.isInitialized) {
      this.initialize();
    }

    try {
      // Normalize query
      const normalizedQuery = query.toLowerCase();

      // Check for company information queries
      if (this.matchesKeywords(normalizedQuery, this.keywordMap.get('company') || [])) {
        return getCompanyInfo();
      }

      // Check for specific product queries
      if (this.matchesKeywords(normalizedQuery, this.keywordMap.get('genex') || [])) {
        return getProductInfo('genex');
      }

      if (this.matchesKeywords(normalizedQuery, this.keywordMap.get('neem') || [])) {
        return getProductInfo('neem');
      }

      if (this.matchesKeywords(normalizedQuery, this.keywordMap.get('santica') || [])) {
        return getProductInfo('santica');
      }

      // Check for general product queries
      if (this.matchesKeywords(normalizedQuery, this.keywordMap.get('products') || [])) {
        return getProductsInfo();
      }

      // Check for problem-specific queries
      if (this.matchesKeywords(normalizedQuery, this.keywordMap.get('pest') || [])) {
        return getPestManagementInfo();
      }

      if (this.matchesKeywords(normalizedQuery, this.keywordMap.get('disease') || [])) {
        return getDiseaseManagementInfo();
      }

      if (this.matchesKeywords(normalizedQuery, this.keywordMap.get('soil') || [])) {
        return getSoilHealthInfo();
      }

      if (this.matchesKeywords(normalizedQuery, this.keywordMap.get('water') || [])) {
        return getWaterManagementInfo();
      }

      // Check for crop-specific queries
      for (const crop of ['rice', 'wheat', 'sugarcane', 'cotton', 'vegetables', 'fruit', 'sandlewood']) {
        if (this.matchesKeywords(normalizedQuery, this.keywordMap.get(crop) || [])) {
          return getCropInfo(crop);
        }
      }

      // If no specific match, try to find relevant keywords
      const relevantTopics = this.findRelevantTopics(normalizedQuery);
      if (relevantTopics.length > 0) {
        // Return information about the most relevant topic
        const topTopic = relevantTopics[0];

        switch (topTopic) {
          case 'company':
            return getCompanyInfo();
          case 'products':
            return getProductsInfo();
          case 'genex':
            return getProductInfo('genex');
          case 'neem':
            return getProductInfo('neem');
          case 'santica':
            return getProductInfo('santica');
          case 'pest':
            return getPestManagementInfo();
          case 'disease':
            return getDiseaseManagementInfo();
          case 'soil':
            return getSoilHealthInfo();
          case 'water':
            return getWaterManagementInfo();
          default:
            if (['rice', 'wheat', 'sugarcane', 'cotton', 'vegetables', 'fruit', 'sandlewood'].includes(topTopic)) {
              return getCropInfo(topTopic);
            }
        }
      }

      // If still no match, return a general response
      return getGeneralResponse();
    } catch (error) {
      // Silent error handling
      return getGeneralResponse();
    }
  }

  private matchesKeywords(query: string, keywords: string[]): boolean {
    return keywords.some(keyword => query.includes(keyword));
  }

  private findRelevantTopics(query: string): string[] {
    const scores: { topic: string; score: number }[] = [];

    // Calculate relevance score for each topic
    for (const [topic, keywords] of this.keywordMap.entries()) {
      let score = 0;

      for (const keyword of keywords) {
        if (query.includes(keyword)) {
          // Add score based on keyword length (longer keywords are more specific)
          score += keyword.length;
        }
      }

      if (score > 0) {
        scores.push({ topic, score });
      }
    }

    // Sort by score (descending) and return topics
    return scores
      .sort((a, b) => b.score - a.score)
      .map(item => item.topic);
  }
}

export default SimpleVectorService;
