import React from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Paper,
  Divider
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import StarIcon from '@mui/icons-material/Star';
import SecurityIcon from '@mui/icons-material/Security';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import pricingContent from '../content/pricing/content.json';

const Pricing = () => {

  return (
    <Container maxWidth="lg" sx={{ py: 6 }}>
      {/* Header Section */}
      <Box sx={{ textAlign: 'center', mb: 8 }}>
        <Typography 
          variant="h2" 
          component="h1" 
          gutterBottom 
          sx={{ 
            fontWeight: 700,
            background: 'linear-gradient(45deg, #1B4C35 30%, #4CAF50 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 2
          }}
        >
          {pricingContent.hero.title}
        </Typography>
        <Typography
          variant="h5"
          color="text.secondary"
          sx={{ mb: 4, maxWidth: '600px', mx: 'auto' }}
        >
          {pricingContent.hero.subtitle}
        </Typography>
      </Box>

      {/* Pricing Card */}
      <Grid container justifyContent="center" sx={{ mb: 6 }}>
        <Grid item xs={12} md={8} lg={6}>
          {pricingContent.plans.map((plan, index) => (
            <Card
              key={index}
              elevation={plan.popular ? 8 : 3}
              sx={{
                position: 'relative',
                borderRadius: '24px',
                overflow: 'visible',
                background: plan.popular 
                  ? 'linear-gradient(135deg, #1B4C35 0%, #2E7D32 100%)'
                  : 'white',
                color: plan.popular ? 'white' : 'inherit',
                transform: plan.popular ? 'scale(1.05)' : 'none',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: plan.popular ? 'scale(1.08)' : 'scale(1.02)',
                  boxShadow: '0 20px 40px rgba(0,0,0,0.15)'
                }
              }}
            >
              {plan.popular && (
                <Chip
                  label="Most Popular"
                  icon={<StarIcon />}
                  sx={{
                    position: 'absolute',
                    top: -12,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    backgroundColor: '#FF6B35',
                    color: 'white',
                    fontWeight: 600,
                    px: 2,
                    py: 1
                  }}
                />
              )}
              
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <Typography variant="h4" component="h3" gutterBottom fontWeight={600}>
                    {plan.name}
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 3, opacity: 0.9 }}>
                    {plan.description}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'baseline', justifyContent: 'center', mb: 2 }}>
                    <Typography variant="h3" component="span" fontWeight={700}>
                      ₹{plan.price.toLocaleString()}
                    </Typography>
                    <Typography variant="h6" component="span" sx={{ ml: 1, opacity: 0.8 }}>
                      /{plan.period}
                    </Typography>
                  </Box>
                  
                  <Button
                    variant={plan.popular ? "outlined" : "contained"}
                    component={RouterLink}
                    to={plan.buttonLink}
                    size="large"
                    fullWidth
                    sx={{
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: '12px',
                      ...(plan.popular ? {
                        borderColor: 'white',
                        color: 'white',
                        '&:hover': {
                          backgroundColor: 'rgba(255,255,255,0.1)',
                          borderColor: 'white'
                        }
                      } : {
                        backgroundColor: '#1B4C35',
                        '&:hover': {
                          backgroundColor: '#0A3622'
                        }
                      })
                    }}
                  >
                    {plan.buttonText}
                  </Button>
                </Box>

                <Divider sx={{ my: 3, borderColor: plan.popular ? 'rgba(255,255,255,0.3)' : undefined }} />

                <List sx={{ p: 0 }}>
                  {plan.features.map((feature, featureIndex) => (
                    <ListItem key={featureIndex} sx={{ px: 0, py: 1 }}>
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        <CheckCircleIcon 
                          sx={{ 
                            color: plan.popular ? '#4CAF50' : '#1B4C35',
                            fontSize: 20 
                          }} 
                        />
                      </ListItemIcon>
                      <ListItemText 
                        primary={feature}
                        primaryTypographyProps={{
                          fontSize: '0.95rem',
                          fontWeight: 500
                        }}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          ))}
        </Grid>
      </Grid>

      {/* Guarantee and Support Section */}
      <Grid container spacing={4} sx={{ mb: 6 }}>
        <Grid item xs={12} md={6}>
          <Paper
            elevation={2}
            sx={{
              p: 3,
              textAlign: 'center',
              borderRadius: '16px',
              background: 'linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 100%)'
            }}
          >
            <SecurityIcon sx={{ fontSize: 48, color: '#1B4C35', mb: 2 }} />
            <Typography variant="h6" gutterBottom fontWeight={600}>
              Money-Back Guarantee
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {pricingContent.guarantee.description}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper
            elevation={2}
            sx={{
              p: 3,
              textAlign: 'center',
              borderRadius: '16px',
              background: 'linear-gradient(135deg, #FFF3E0 0%, #FFF8E1 100%)'
            }}
          >
            <SupportAgentIcon sx={{ fontSize: 48, color: '#FF6B35', mb: 2 }} />
            <Typography variant="h6" gutterBottom fontWeight={600}>
              {pricingContent.support.title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {pricingContent.support.description}
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* FAQ Section */}
      <Box sx={{ textAlign: 'center', mt: 8 }}>
        <Typography variant="h4" gutterBottom fontWeight={600}>
          Frequently Asked Questions
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Have questions? We're here to help.
        </Typography>
        <Button
          variant="outlined"
          component={RouterLink}
          to="/faq"
          size="large"
          sx={{
            borderRadius: '12px',
            px: 4,
            py: 1.5,
            borderColor: '#1B4C35',
            color: '#1B4C35',
            '&:hover': {
              backgroundColor: '#1B4C35',
              color: 'white'
            }
          }}
        >
          View All FAQs
        </Button>
      </Box>
    </Container>
  );
};

export default Pricing;
