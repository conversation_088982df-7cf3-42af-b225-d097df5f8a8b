import { useState, useEffect } from 'react';

// CMS Content Types
export interface CMSSettings {
  site: {
    title: string;
    description: string;
    url: string;
    logo?: string;
    favicon?: string;
  };
  contact: {
    phone: string;
    email: string;
    address: string;
    workingHours: string;
    gstNumber: string;
  };
  social: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
    youtube?: string;
  };
}

export interface PaymentSettings {
  registration: {
    baseAmount: number;
    gstPercentage: number;
    totalAmount: number;
    currency: string;
    currencySymbol: string;
  };
  gateway: {
    name: string;
    testMode: boolean;
    timeout: number;
  };
  methods: {
    creditCards: boolean;
    debitCards: boolean;
    netBanking: boolean;
    upi: boolean;
    wallets: boolean;
  };
}

export interface ReceiptSettings {
  header: {
    companyName: string;
    title: string;
    gstNumber: string;
    website: string;
    phone: string;
  };
  footer: {
    thankYou: string;
    successMessage: string;
    supportMessage: string;
    tagline: string;
    signatureNote: string;
  };
  styling: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
    fontSize: number;
  };
}

export interface UIContent {
  buttons: {
    primary: Record<string, string>;
    secondary: Record<string, string>;
    loading: Record<string, string>;
  };
  messages: {
    success: Record<string, string>;
    error: Record<string, string>;
    validation: Record<string, string>;
  };
  labels: {
    fields: Record<string, string>;
    placeholders: Record<string, string>;
  };
}

export interface FeatureFlags {
  payment: {
    enablePayment: boolean;
    enableReceipt: boolean;
    enableEmailNotifications: boolean;
    enableSmsNotifications: boolean;
  };
  form: {
    enableMultiLanguage: boolean;
    enableAutoSave: boolean;
    enableProgressBar: boolean;
    enableValidation: boolean;
  };
  ui: {
    enableDarkMode: boolean;
    enableAnimations: boolean;
    enableLoadingStates: boolean;
    enableTooltips: boolean;
  };
}

// Custom hook to load CMS content
export const useCMSContent = () => {
  const [cmsSettings, setCMSSettings] = useState<CMSSettings | null>(null);
  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings | null>(null);
  const [receiptSettings, setReceiptSettings] = useState<ReceiptSettings | null>(null);
  const [uiContent, setUIContent] = useState<UIContent | null>(null);
  const [featureFlags, setFeatureFlags] = useState<FeatureFlags | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadCMSContent = async () => {
      try {
        setLoading(true);
        
        // Load all CMS content files
        const [
          cmsResponse,
          paymentResponse,
          receiptResponse,
          buttonsResponse,
          messagesResponse,
          labelsResponse,
          featuresResponse
        ] = await Promise.all([
          import('../config/cms-settings.json'),
          import('../config/payment-settings.json'),
          import('../config/receipt-settings.json'),
          import('../content/ui/buttons.json'),
          import('../content/ui/messages.json'),
          import('../content/ui/labels.json'),
          import('../config/features.json')
        ]);

        setCMSSettings(cmsResponse.default);
        setPaymentSettings(paymentResponse.default);
        setReceiptSettings(receiptResponse.default);
        setUIContent({
          buttons: buttonsResponse.default,
          messages: messagesResponse.default,
          labels: labelsResponse.default
        });
        setFeatureFlags(featuresResponse.default);
        
        setError(null);
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error('Failed to load CMS content:', err);
        setError('Failed to load content');
      } finally {
        setLoading(false);
      }
    };

    loadCMSContent();
  }, []);

  return {
    cmsSettings,
    paymentSettings,
    receiptSettings,
    uiContent,
    featureFlags,
    loading,
    error
  };
};

// Helper hooks for specific content types
export const useUIContent = () => {
  const { uiContent, loading, error } = useCMSContent();
  return { uiContent, loading, error };
};

export const usePaymentSettings = () => {
  const { paymentSettings, loading, error } = useCMSContent();
  return { paymentSettings, loading, error };
};

export const useReceiptSettings = () => {
  const { receiptSettings, loading, error } = useCMSContent();
  return { receiptSettings, loading, error };
};

export const useFeatureFlags = () => {
  const { featureFlags, loading, error } = useCMSContent();
  return { featureFlags, loading, error };
};

export const useSiteSettings = () => {
  const { cmsSettings, loading, error } = useCMSContent();
  return { siteSettings: cmsSettings, loading, error };
};
