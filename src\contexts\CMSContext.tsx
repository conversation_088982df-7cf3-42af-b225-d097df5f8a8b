import React, { createContext, useContext, ReactNode } from 'react';
import { 
  useCMSContent, 
  CMSSettings, 
  PaymentSettings, 
  ReceiptSettings, 
  UIContent, 
  FeatureFlags 
} from '../hooks/useCMSContent';

interface CMSContextType {
  cmsSettings: CMSSettings | null;
  paymentSettings: PaymentSettings | null;
  receiptSettings: ReceiptSettings | null;
  uiContent: UIContent | null;
  featureFlags: FeatureFlags | null;
  loading: boolean;
  error: string | null;
}

const CMSContext = createContext<CMSContextType | undefined>(undefined);

interface CMSProviderProps {
  children: ReactNode;
}

export const CMSProvider: React.FC<CMSProviderProps> = ({ children }) => {
  const cmsData = useCMSContent();

  return (
    <CMSContext.Provider value={cmsData}>
      {children}
    </CMSContext.Provider>
  );
};

export const useCMS = () => {
  const context = useContext(CMSContext);
  if (context === undefined) {
    throw new Error('useCMS must be used within a CMSProvider');
  }
  return context;
};

// Specific context hooks
export const useCMSSettings = () => {
  const { cmsSettings } = useCMS();
  return cmsSettings;
};

export const useCMSPaymentSettings = () => {
  const { paymentSettings } = useCMS();
  return paymentSettings;
};

export const useCMSReceiptSettings = () => {
  const { receiptSettings } = useCMS();
  return receiptSettings;
};

export const useCMSUIContent = () => {
  const { uiContent } = useCMS();
  return uiContent;
};

export const useCMSFeatureFlags = () => {
  const { featureFlags } = useCMS();
  return featureFlags;
};
