/**
 * PayU Payment Gateway Service
 * Handles payment initiation, verification, and status checking
 */

import config from '../config';

// PayU Payment Request Interface
export interface PayUPaymentRequest {
  txnid: string;
  amount: number;
  productinfo: string;
  firstname: string;
  email: string;
  phone: string;
  address1?: string;
  city?: string;
  state?: string;
  country?: string;
  zipcode?: string;
  udf1?: string;
  udf2?: string;
  udf3?: string;
  udf4?: string;
  udf5?: string;
}

// PayU Payment Response Interface
export interface PayUPaymentResponse {
  success: boolean;
  data?: {
    paymentUrl?: string;
    txnid: string;
    hash: string;
    key: string;
    amount: number;
  };
  message: string;
  error?: string;
}

// PayU Payment Status Interface
export interface PayUPaymentStatus {
  success: boolean;
  data?: {
    status: string;
    txnid: string;
    amount: number;
    productinfo: string;
    firstname: string;
    email: string;
    phone: string;
    mihpayid?: string;
    mode?: string;
    bank_ref_num?: string;
    bankcode?: string;
    name_on_card?: string;
    cardnum?: string;
    issuing_bank?: string;
    card_type?: string;
  };
  message: string;
  error?: string;
}

class PayUService {
  private readonly functionsUrl: string;

  constructor() {
    this.functionsUrl = config.payu.functionsUrl;
  }

  /**
   * Generate a unique transaction ID
   */
  private generateTransactionId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `DARVI_${timestamp}_${random}`;
  }

  /**
   * Validate payment amount
   */
  private validateAmount(amount: number): boolean {
    return amount >= config.validation.minPaymentAmount &&
           amount <= config.validation.maxPaymentAmount;
  }

  /**
   * Submit payment form to PayU Hosted Checkout
   */
  private submitPaymentForm(paymentData: any, paymentUrl: string): void {
    // Show loading indicator
    this.showPaymentLoading();

    try {
      // Create a form element for PayU Hosted Checkout
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = paymentUrl;
      form.style.display = 'none';
      form.target = '_self'; // Redirect in same window for hosted checkout

      // Add all payment data as hidden inputs
      Object.entries(paymentData).forEach(([key, value]) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = String(value);
        form.appendChild(input);
      });

      // Add hosted checkout specific parameters
      if (config.payu.hostedCheckout.enableNativeUI) {
        const nativeUIInput = document.createElement('input');
        nativeUIInput.type = 'hidden';
        nativeUIInput.name = 'enforce_paymethod';
        nativeUIInput.value = 'creditcard|debitcard|netbanking|upi|wallet';
        form.appendChild(nativeUIInput);
      }

      // Add language preference
      const langInput = document.createElement('input');
      langInput.type = 'hidden';
      langInput.name = 'pg_language';
      langInput.value = config.payu.hostedCheckout.language;
      form.appendChild(langInput);

      // Add form to document and submit to PayU Hosted Checkout
      document.body.appendChild(form);

      // Add a small delay to ensure loading indicator is visible
      setTimeout(() => {
        try {
          form.submit();

          // Clean up form after submission
          setTimeout(() => {
            if (form.parentNode) {
              form.parentNode.removeChild(form);
            }
          }, 1000);
        } catch (submitError) {
          // Fallback: Try using window.location with GET parameters
          this.fallbackRedirect(paymentData, paymentUrl);
        }
      }, 100);
    } catch (error) {
      // Fallback: Try using window.location with GET parameters
      this.fallbackRedirect(paymentData, paymentUrl);
    }
  }

  /**
   * Fallback redirect method using GET parameters
   */
  private fallbackRedirect(paymentData: any, paymentUrl: string): void {
    // Create URL with GET parameters as fallback
    const params = new URLSearchParams();
    Object.entries(paymentData).forEach(([key, value]) => {
      params.append(key, String(value));
    });

    // Add language preference
    params.append('pg_language', config.payu.hostedCheckout.language);

    const redirectUrl = `${paymentUrl}?${params.toString()}`;

    // Use window.location for redirect
    window.location.href = redirectUrl;
  }

  /**
   * Show payment loading indicator
   */
  private showPaymentLoading(): void {
    // Create loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'payu-loading-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      font-family: Arial, sans-serif;
    `;

    const loadingContent = document.createElement('div');
    loadingContent.style.cssText = `
      background: white;
      padding: 30px;
      border-radius: 10px;
      text-align: center;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    `;

    loadingContent.innerHTML = `
      <div style="margin-bottom: 20px;">
        <div style="
          border: 4px solid #f3f3f3;
          border-top: 4px solid #1976d2;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          animation: spin 1s linear infinite;
          margin: 0 auto;
        "></div>
      </div>
      <h3 style="margin: 0 0 10px 0; color: #333;">Redirecting to PayU</h3>
      <p style="margin: 0; color: #666;">Please wait while we redirect you to the secure payment page...</p>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `;

    overlay.appendChild(loadingContent);
    document.body.appendChild(overlay);

    // Auto-remove overlay after 10 seconds (fallback)
    setTimeout(() => {
      const existingOverlay = document.getElementById('payu-loading-overlay');
      if (existingOverlay) {
        existingOverlay.remove();
      }
    }, 10000);
  }

  /**
   * Initiate PayU payment
   */
  async initiatePayment(formData: any): Promise<PayUPaymentResponse> {
    try {
      // Validate payment amount
      if (!this.validateAmount(config.payu.paymentAmount)) {
        throw new Error('Invalid payment amount');
      }

      // Generate transaction ID
      const txnid = this.generateTransactionId();

      // Validate required email field
      const email = formData.email;
      if (!email || !this.isValidEmail(email)) {
        throw new Error('Valid email address is required for payment processing');
      }

      // Prepare payment request
      const paymentRequest: PayUPaymentRequest = {
        txnid,
        amount: config.payu.paymentAmount,
        productinfo: 'Darvi Group Farmer Registration',
        firstname: formData.name || formData.customerName,
        email: email, // Use validated email from form
        phone: formData.mobile || formData.customerMobile,
        address1: formData.address,
        city: formData.city,
        state: 'Karnataka',
        country: 'India',
        zipcode: formData.pincode || '560001',
        udf1: formData.landArea,
        udf2: formData.soilType,
        udf3: formData.district,
        udf4: formData.taluk,
        udf5: JSON.stringify({ registrationData: formData })
      };

      // Call Netlify function to initiate payment
      const response = await fetch(`${this.functionsUrl}${config.payu.endpoints.initiate}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentRequest)
      });

      if (!response.ok) {
        // Try to get error details from response
        let errorData: any = null;

        try {
          const errorText = await response.text();

          // Try to parse as JSON for structured error
          try {
            errorData = JSON.parse(errorText);
          } catch (e) {
            // Not JSON, use text as is
          }
        } catch (e) {
          // Could not read error response
        }

        // Create user-friendly error message
        let userMessage = 'Failed to initiate payment. Please try again.';

        if (errorData?.message) {
          userMessage = errorData.message;
        } else if (response.status === 500) {
          userMessage = 'Payment service is temporarily unavailable. Please try again later.';
        } else if (response.status === 400) {
          userMessage = 'Invalid payment information. Please check your details.';
        } else if (response.status === 403) {
          userMessage = 'Payment service access denied. Please contact support.';
        }

        throw new Error(userMessage);
      }

      const result = await response.json();

      if (result.success && result.data) {
        // Store transaction details for later verification
        localStorage.setItem('payuTransaction', JSON.stringify({
          txnid: result.data.txnid,
          amount: config.payu.paymentAmount,
          formData,
          timestamp: new Date().toISOString()
        }));

        // Store complete form data for payment response handling
        localStorage.setItem('payuCheckoutData', JSON.stringify({
          ...formData,
          txnid: result.data.txnid,
          amount: config.payu.paymentAmount,
          timestamp: new Date().toISOString()
        }));

        // Create and submit form to PayU
        this.submitPaymentForm(result.data.paymentData, result.data.paymentUrl);

        // Return success response
        return {
          success: true,
          message: 'Redirecting to PayU...',
          data: {
            txnid: result.data.txnid,
            hash: result.data.hash,
            key: result.data.key,
            amount: result.data.amount,
            paymentUrl: result.data.paymentUrl
          }
        };
      }

      return result;
    } catch (error) {
      // Handle error gracefully for production
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to initiate payment. Please try again.',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Verify payment status
   */
  async verifyPayment(txnid: string): Promise<PayUPaymentStatus> {
    try {
      const response = await fetch(`${this.functionsUrl}${config.payu.endpoints.verify}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ txnid })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        message: 'Failed to verify payment status. Please contact support.',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get stored transaction details
   */
  getStoredTransaction(): any {
    try {
      const stored = localStorage.getItem('payuTransaction');
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Clear stored transaction details
   */
  clearStoredTransaction(): void {
    try {
      localStorage.removeItem('payuTransaction');
    } catch (error) {
      // Error clearing stored transaction - silently handle
    }
  }

  /**
   * Validate payment response from PayU
   */
  validatePaymentResponse(response: any): boolean {
    try {
      // Basic validation of required fields
      return !!(
        response.txnid &&
        response.status &&
        response.amount &&
        response.firstname &&
        response.email
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Format amount for display
   */
  formatAmount(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  }

  /**
   * Get payment configuration for frontend
   */
  getPaymentConfig() {
    return {
      amount: config.payu.paymentAmount,
      amountDisplay: config.payu.paymentAmountDisplay,
      companyName: config.payu.companyName,
      environment: config.payu.environment,
      successUrl: config.payu.successUrl,
      failureUrl: config.payu.failureUrl,
      cancelUrl: config.payu.cancelUrl,
      hostedCheckout: config.payu.hostedCheckout
    };
  }

  /**
   * Get hosted checkout features information
   */
  getHostedCheckoutFeatures() {
    return {
      paymentModes: config.payu.hostedCheckout.paymentModes,
      security: config.payu.hostedCheckout.security,
      userExperience: config.payu.hostedCheckout.userExperience,
      customization: config.payu.hostedCheckout.customization,
      branding: config.payu.hostedCheckout.branding
    };
  }

  /**
   * Check if mobile device for UPI intent
   */
  isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  /**
   * Get recommended payment methods based on device and user
   */
  getRecommendedPaymentMethods(): string[] {
    const recommendations: string[] = [];

    if (this.isMobileDevice()) {
      // Prioritize UPI for mobile users
      if (config.payu.hostedCheckout.paymentModes.upi) {
        recommendations.push('UPI');
      }
      if (config.payu.hostedCheckout.paymentModes.wallets) {
        recommendations.push('Digital Wallets');
      }
    }

    // Add cards for all users
    if (config.payu.hostedCheckout.paymentModes.cards) {
      recommendations.push('Credit/Debit Cards');
    }

    // Add net banking
    if (config.payu.hostedCheckout.paymentModes.netBanking) {
      recommendations.push('Net Banking');
    }

    return recommendations;
  }

  /**
   * Validate hosted checkout configuration
   */
  validateHostedCheckoutConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if at least one payment mode is enabled
    const paymentModes = config.payu.hostedCheckout.paymentModes;
    const enabledModes = Object.values(paymentModes).some(mode =>
      typeof mode === 'boolean' ? mode : false
    );

    if (!enabledModes) {
      errors.push('At least one payment mode must be enabled');
    }

    // Validate branding configuration
    const branding = config.payu.hostedCheckout.branding;
    if (branding.merchantLogo && !branding.merchantLogo.startsWith('http')) {
      errors.push('Merchant logo must be a valid URL');
    }

    // Validate language
    const supportedLanguages = ['en', 'hi', 'te', 'ta', 'kn', 'ml', 'bn', 'gu', 'mr', 'pa'];
    if (!supportedLanguages.includes(config.payu.hostedCheckout.language)) {
      errors.push('Unsupported language selected');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get payment status message for user
   */
  getPaymentStatusMessage(status: string, mode?: string): string {
    switch (status.toLowerCase()) {
      case 'success':
        return `Payment completed successfully${mode ? ` via ${mode}` : ''}!`;
      case 'failure':
        return 'Payment failed. Please try again with a different payment method.';
      case 'pending':
        return 'Payment is being processed. You will receive a confirmation shortly.';
      case 'cancelled':
        return 'Payment was cancelled. You can try again when ready.';
      default:
        return 'Payment status unknown. Please contact support for assistance.';
    }
  }

  /**
   * Validate email address format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }
}

// Export singleton instance
const payuService = new PayUService();
export default payuService;
