import axios from 'axios';
import SimpleVectorService from './simpleVectorService';
import { darviContentData } from '../data/darviContentData';

// Define the context information about Darvi Group to guide the AI responses
const DARVI_CONTEXT = `
You are an AI assistant for Darvi Group, a company focused on sustainable agricultural solutions. Your name is Darvi Assistant.

About Darvi Group:
- Founded in 2018
- Provides sustainable agricultural solutions
- Offers consultation, site visits, crop management guidance, irrigation solutions
- Promotes sustainable farming practices
- Has IoT-powered agriculture solutions for monitoring crops
- Sells products like Genex, Neem, and Santica

Key services:
- Agricultural consultation: Expert advice on crop selection, soil management, and farming techniques
- Sustainable farming practices: Eco-friendly methods that reduce environmental impact
- IoT-powered agriculture monitoring: Real-time tracking of soil moisture, temperature, and other metrics
- Land management advice: Optimizing land use for maximum productivity
- Farmer registration services: Helping farmers register for government programs and subsidies

Products:
- Genex: Enhances soil fertility, improves crop yield
- Neem: Natural pest control, organic farming solution
- Santica: Improves plant health, increases productivity
- Store: https://mybillbook.in/store/darvi_group

Contact Information:
- Address: Darvi Group, Hubli, Karnataka 580030, India
- Phone: +91 99868 90777
- Email: <EMAIL>
- Website: https://darvigroup.com
- Store: https://mybillbook.in/store/darvi_group
- IoT Portal: https://iot.haegl.in

Important Guidelines:
1. Keep all responses extremely concise and to the point
2. For product information, provide only: name, price, and 1-2 key benefits
3. Use simple bullet points without excessive formatting
4. Avoid lengthy introductions or conclusions
5. For product queries, respond with just the product list in bullet format
6. Limit responses to 3-5 lines whenever possible
7. Use direct language without unnecessary words
8. For unrelated questions, provide a brief redirection
9. Never use marketing language or excessive descriptions
10. Present information in the most compact format possible
`;

// Convert the full darviContentData to a string to include in the context
const DARVI_FULL_DATA = JSON.stringify(darviContentData, null, 2);

class GeminiService {
  private apiKey: string;
  private apiUrl: string;
  private isConfigured: boolean;
  private conversationHistory: { role: string; parts: { text: string }[] }[];

  constructor() {
    this.apiKey = process.env.REACT_APP_GEMINI_API_KEY || 'AIzaSyAELAzH3ziO9EWCLcNhAlSGYPjyh4Dt0aI';
    this.apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
    this.isConfigured = true;
    this.conversationHistory = [
      {
        role: 'user',
        parts: [{ text: DARVI_CONTEXT }]
      }
    ];
  }

  async generateResponse(userMessage: string): Promise<string> {
    if (!this.isConfigured) {
      return 'I apologize, but I am not properly configured at the moment. Please contact Darvi Group for assistance.';
    }

    try {
      // Add user message to conversation history
      this.conversationHistory.push({
        role: 'user',
        parts: [{ text: userMessage }]
      });

      // First, try to get a response from Gemini API with the full context data
      const requestData = {
        contents: [
          {
            parts: [
              {
                text: `${DARVI_CONTEXT}\n\nHere is the complete data about Darvi Group, its products, services, and solutions:\n\n${DARVI_FULL_DATA}\n\nBased on the above data, please answer the following user question:\n\nUser: ${userMessage}`
              }
            ]
          }
        ]
      };

      const response = await axios.post(
        `${this.apiUrl}?key=${this.apiKey}`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.candidates && response.data.candidates.length > 0) {
        let aiResponse = response.data.candidates[0].content.parts[0].text;

        // Format the response to ensure proper bullet points and highlighting
        aiResponse = this.formatResponse(aiResponse);

        // Add AI response to conversation history
        this.conversationHistory.push({
          role: 'model',
          parts: [{ text: aiResponse }]
        });

        // Keep conversation history manageable
        if (this.conversationHistory.length > 10) {
          this.conversationHistory = [
            this.conversationHistory[0], // Keep the context
            ...this.conversationHistory.slice(-4) // Keep the last 2 exchanges
          ];
        }

        return aiResponse;
      }

      // If Gemini API fails, fall back to the simple vector service
      const vectorService = SimpleVectorService.getInstance();
      const contentResponse = vectorService.searchContent(userMessage);

      if (contentResponse) {
        // Add content response to conversation history
        this.conversationHistory.push({
          role: 'model',
          parts: [{ text: contentResponse }]
        });

        return contentResponse;
      }

      // If both Gemini and vector service fail, return a helpful general response
      return `I can help you with information about Darvi Group's agricultural products and services.

Please feel free to ask about our company, products (Genex, Neem, Santica), crop management solutions, or common agricultural problems.`;
    } catch (error) {
      // Silent error handling for production

      // If there's an error with Gemini, try the vector service as a fallback
      try {
        const vectorService = SimpleVectorService.getInstance();
        const contentResponse = vectorService.searchContent(userMessage);

        if (contentResponse) {
          return contentResponse;
        }
      } catch (vectorError) {
        // Both Gemini and vector service failed
      }

      // Even if there's an error, try to provide a helpful response
      return `I apologize for the technical difficulty. I can still help with basic information about Darvi Group:

• We offer agricultural and forestry solutions
• Our products include Genex, Neem, and Santica
• We provide consultation services and IoT solutions
• Contact us at +91 99868 90777 for personalized assistance`;
    }
  }

  private formatResponse(response: string): string {
    // Ensure bullet points are properly formatted
    response = response.replace(/\n\s*[-•]\s*/g, '\n• ');

    // Ensure prices are highlighted
    response = response.replace(/(₹\d+,\d+|\d+,\d+)/g, '**$1**');

    // Ensure links are properly formatted
    response = response.replace(/(https?:\/\/[^\s]+)/g, '[$1]($1)');

    return response;
  }

  // Method to check if a query is related to Darvi Group or agriculture
  isRelevantQuery(query: string): boolean {
    const relevantKeywords = [
      // Company related
      'darvi', 'group', 'company', 'business', 'organization', 'firm',

      // Agriculture related
      'agriculture', 'farming', 'crop', 'plant', 'soil', 'irrigation', 'field',
      'sustainable', 'organic', 'fertilizer', 'pesticide', 'harvest', 'seed',
      'farmer', 'land', 'cultivation', 'grow', 'farm', 'forestry', 'garden',
      'yield', 'produce', 'growth', 'planting', 'sowing', 'agricultural',

      // IoT related
      'iot', 'monitoring', 'sensor', 'technology', 'smart', 'device', 'system',
      'data', 'tracking', 'automation', 'digital', 'tech', 'solution',

      // Products and services
      'product', 'service', 'genex', 'neem', 'santica', 'consultation',
      'registration', 'management', 'advice', 'support', 'help', 'assist',

      // Business related
      'contact', 'location', 'price', 'cost', 'payment', 'buy', 'purchase',
      'order', 'delivery', 'shipping', 'store', 'shop', 'website', 'online',

      // Expertise related
      'expert', 'advice', 'guidance', 'recommendation', 'suggestion', 'tip',
      'information', 'knowledge', 'experience', 'skill', 'specialty',

      // Location related
      'Karnataka', 'India', 'Hubli', 'address', 'office', 'headquarters',

      // Common questions
      'what', 'how', 'when', 'where', 'why', 'who', 'which', 'can', 'do', 'does'
    ];

    // If the query is very short (less than 4 characters), consider it relevant
    // to avoid filtering out short queries like "IoT" or "help"
    if (query.trim().length < 4) return true;

    const queryLower = query.toLowerCase();

    // Check if any of the keywords are in the query
    return relevantKeywords.some(keyword => queryLower.includes(keyword.toLowerCase()));
  }
}

// Create and export a singleton instance
const geminiService = new GeminiService();

// Initialize the SimpleVectorService to make sure it's ready
try {
  SimpleVectorService.getInstance();
  // Service initialized successfully
} catch (error) {
  // Handle error silently
}

export default geminiService;
