{"name": "darvi-payu-netlify-functions", "version": "1.0.0", "description": "PayU payment gateway integration for Darvi Group using Netlify Functions", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["payu", "payment", "gateway", "netlify", "functions", "darvi"], "author": "Darvi Group", "license": "MIT", "dependencies": {}, "engines": {"node": ">=14.0.0"}}