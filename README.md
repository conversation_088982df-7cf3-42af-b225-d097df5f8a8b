# Darvi Group - Agricultural Services Platform

A comprehensive web application built with React for agricultural services and farmer registration. This platform provides end-to-end solutions for modern farming needs.

## 🌟 Key Features

### **Multi-language Support**
- English, Kannada, and Hindi language options
- Seamless language switching across all pages
- Culturally localized content

### **Complete Service Platform**
- **Services Page**: Detailed agricultural consultation services
- **Pricing Page**: Transparent pricing with feature breakdown
- **Checkout Page**: Streamlined registration processing
- **Registration System**: Comprehensive farmer onboarding

### **Registration System**
- Streamlined farmer registration process
- Secure data collection and processing
- Real-time form validation and submission
- Production-ready registration workflow

### **Smart Form Management**
- Comprehensive farmer information collection
- Real-time form validation with user feedback
- Mobile-optimized input fields
- Error handling and recovery

### **Data Management**
- Automated Google Sheets integration
- Real-time data synchronization
- Secure data storage and processing
- Registration tracking and management

### **User Experience**
- Responsive design for all devices (mobile, tablet, desktop)
- Modern UI with Material-UI components
- Smooth animations and transitions
- Intuitive navigation and user flow

## 🛠️ Technical Stack

### **Frontend**
- **Framework**: React 18 with TypeScript
- **UI Library**: Material-UI (MUI) v5
- **Routing**: React Router v6
- **State Management**: React Context API
- **Form Handling**: Formik with Yup validation
- **Styling**: CSS-in-JS with MUI's styled system

### **Backend & Services**
- **Serverless Functions**: Netlify Functions

- **Data Storage**: Google Sheets API
- **Email Service**: Google Apps Script integration
- **Authentication**: Environment-based configuration

### **Development & Deployment**
- **Build Tool**: Create React App with TypeScript
- **Package Manager**: npm
- **Deployment**: Netlify with continuous deployment
- **Environment Management**: Environment variables for production/development

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v16 or higher)
- **npm** package manager
- **Google Apps Script** setup for data integration

- **Netlify Account** for deployment

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/darvi-registration.git
   cd darvi-registration
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**

   Create environment files for different stages:

   **Development (.env.development)**
   ```env
   REACT_APP_GOOGLE_SCRIPT_URL=your_google_script_url_here
   REACT_APP_CONTACT_NUMBER=your_contact_number_here
   REACT_APP_EMAIL=your_email_here
   ```

   **Production (.env.production)**
   ```env
   REACT_APP_GOOGLE_SCRIPT_URL=your_production_google_script_url
   REACT_APP_CONTACT_NUMBER=your_contact_number
   REACT_APP_EMAIL=your_email
   ```

### Development

1. **Start the development server:**
   ```bash
   npm start
   ```
   The application will open at `http://localhost:3000`

2. **Available Scripts:**
   ```bash
   npm start          # Start development server
   npm run build      # Create production build
   npm test           # Run tests
   npm run eject      # Eject from Create React App (not recommended)
   ```

### Production Deployment

1. **Build for production:**
   ```bash
   npm run build
   ```

2. **Deploy to Netlify:**
   - Connect your GitHub repository to Netlify
   - Set build command: `npm run build`
   - Set publish directory: `build`
   - Configure environment variables in Netlify dashboard

## 📝 Environment Variables

### Frontend Variables (React App)
| Variable | Description | Required |
|----------|-------------|----------|
| `REACT_APP_GOOGLE_SCRIPT_URL` | URL for Google Apps Script integration | Yes |
| `REACT_APP_CONTACT_NUMBER` | Support contact number | Yes |
| `REACT_APP_EMAIL` | Support email address | Yes |



## 🏗️ Project Structure

```
darvi-registration/
├── public/                 # Static assets
├── src/
│   ├── components/        # Reusable React components
│   ├── pages/            # Page components (Home, Services, Pricing, etc.)
│   ├── services/         # API services (Google Sheets, Email)
│   ├── contexts/         # React Context providers
│   ├── utils/            # Utility functions
│   ├── data/             # Static data and content
│   └── types/            # TypeScript type definitions
├── netlify/
│   └── functions/        # Serverless functions for backend services
├── scripts/              # Build and deployment scripts
└── package.json          # Project dependencies and scripts
```

## 🚀 Features Overview

### **User Journey**
1. **Home Page** → Overview and quick access to services
2. **Services Page** → Detailed service descriptions and benefits
3. **Pricing Page** → Transparent pricing with feature breakdown
4. **Checkout Page** → Secure payment processing
5. **Payment Success** → Confirmation and next steps

### **Key Pages**
- **`/`** - Home page with service overview
- **`/services`** - Detailed services and benefits
- **`/pricing`** - Pricing information and plans
- **`/checkout`** - Payment processing page
- **`/about`** - About Darvi Group
- **`/contact`** - Contact information
- **`/faq`** - Frequently asked questions

## 🔧 Development Guidelines

### **Code Standards**
- Use TypeScript for type safety
- Follow React functional components with hooks
- Use Material-UI components consistently
- Implement responsive design principles
- Add proper error handling and loading states

### **File Organization**
- Components: Reusable UI components
- Pages: Route-specific page components
- Services: API integration and business logic
- Utils: Helper functions and utilities
- Types: TypeScript type definitions

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support, please contact:
- **Phone**: +91 **********
- **Email**: <EMAIL>
- **Website**: [Darvi Group](https://darvi-registration.netlify.app)

---

**Made with ❤️ by Darvi Group**
*Empowering farmers with modern agricultural solutions*
