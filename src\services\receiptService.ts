/**
 * Receipt Service
 * Handles receipt data generation, client ID creation, and PDF generation
 */

// import { v4 as uuidv4 } from 'uuid'; // Unused for now
import { storeClientId } from '../utils/clientIdStorage';

// Receipt Data Interface
export interface ReceiptData {
  // Transaction Details
  transactionId: string;
  paymentId: string;
  paymentDate: string;
  paymentMethod: string;
  status: string;
  clientId: string;

  // PayU Specific Transaction Details
  mihpayid?: string;           // PayU transaction number
  bankReferenceNumber?: string; // Bank reference number
  paymentGateway?: string;     // Payment gateway used
  paymentMode?: string;        // Specific payment mode (CC, DC, NB, UPI, etc.)

  // Amount Breakdown
  baseAmount: number;
  gstAmount: number;
  platformFeeAmount: number;
  totalAmount: number;

  // Customer Information
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerAddress?: string;
  customerCity?: string;
  customerPincode?: string;

  // Registration Details
  landArea?: string;
  soilType?: string;
  district?: string;
  taluk?: string;

  // Additional Data
  additionalData?: Record<string, any>;
}

/**
 * Generate a unique client ID for the transaction
 * Format: DG + 4 random characters (uppercase letters and numbers)
 */
export function generateClientId(): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = 'DG';

  for (let i = 0; i < 4; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
}

/**
 * Generate a unique receipt ID
 */
export function generateReceiptId(): string {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  return `RECEIPT_${timestamp}_${random}`;
}

/**
 * Create receipt data with consistent client ID
 */
export function createReceiptData(
  paymentData: any,
  formData: any,
  transactionDetails: any
): ReceiptData {
  // Generate unique client ID for this transaction
  const clientId = generateClientId();

  // Calculate amount breakdown (base ₹4500 + GST 5% = ₹4725)
  const totalAmount = parseFloat(paymentData?.amount) || 4725;
  const baseAmount = Math.round(totalAmount / 1.05); // Calculate base from total
  const gstAmount = totalAmount - baseAmount; // GST is the difference
  const platformFeeAmount = 0; // No platform fee

  // Extract customer information with better fallbacks
  const customerName = formData?.name ||
                      formData?.customerName ||
                      paymentData?.firstname ||
                      transactionDetails?.firstname ||
                      formData?.farmerName ||
                      'Customer';

  const customerEmail = formData?.email ||
                       paymentData?.email ||
                       transactionDetails?.email ||
                       formData?.customerEmail ||
                       '<EMAIL>';

  const customerPhone = formData?.mobile ||
                       formData?.customerMobile ||
                       formData?.phone ||
                       paymentData?.phone ||
                       transactionDetails?.phone ||
                       formData?.phoneNumber ||
                       '';

  // Extract address information with better handling
  const customerAddress = formData?.address ||
                         formData?.fullAddress ||
                         paymentData?.address1 ||
                         transactionDetails?.address1 ||
                         '';

  const customerCity = formData?.city ||
                      formData?.district ||
                      paymentData?.city ||
                      transactionDetails?.city ||
                      '';

  const customerPincode = formData?.pincode ||
                         formData?.zipcode ||
                         paymentData?.zipcode ||
                         transactionDetails?.zipcode ||
                         '';

  // Extract transaction details with better validation
  const transactionId = paymentData?.txnid ||
                       transactionDetails?.txnid ||
                       paymentData?.transactionId ||
                       `TXN${Date.now()}`;

  const paymentId = paymentData?.mihpayid ||
                   transactionDetails?.mihpayid ||
                   paymentData?.paymentId ||
                   '';

  // Better timestamp handling
  const paymentDate = paymentData?.timestamp ||
                     transactionDetails?.timestamp ||
                     paymentData?.paymentTimestamp ||
                     paymentData?.addedon ||
                     new Date().toISOString();

  // Enhanced payment method detection
  const paymentMethod = formatPaymentMethod(
    paymentData?.mode ||
    transactionDetails?.mode ||
    paymentData?.paymentMethod ||
    'PayU'
  );

  const status = formatStatus(
    paymentData?.status ||
    transactionDetails?.status ||
    'success'
  );

  // Extract payment mode details for better display
  const paymentMode = paymentData?.paymentMode ||
                     paymentData?.mode ||
                     transactionDetails?.paymentMode ||
                     paymentData?.bankcode ||
                     paymentMethod;

  // Extract PayU specific transaction details
  const mihpayid = paymentData?.mihpayid ||
                  transactionDetails?.mihpayid ||
                  paymentId;

  const bankReferenceNumber = paymentData?.bank_ref_num ||
                             transactionDetails?.bank_ref_num ||
                             paymentData?.bankReferenceNumber ||
                             transactionDetails?.bankReferenceNumber;

  const paymentGateway = paymentData?.pg_type ||
                        transactionDetails?.pg_type ||
                        paymentData?.paymentGateway ||
                        'PayU';

  // Extract registration details
  const landArea = formData?.landArea || 
                  paymentData?.udf1 || 
                  transactionDetails?.udf1 || 
                  '';

  const soilType = formData?.soilType || 
                  paymentData?.udf2 || 
                  transactionDetails?.udf2 || 
                  '';

  const district = formData?.district || 
                  paymentData?.udf3 || 
                  transactionDetails?.udf3 || 
                  '';

  const taluk = formData?.taluk || 
               paymentData?.udf4 || 
               transactionDetails?.udf4 || 
               '';

  // Prepare additional data
  const additionalData: Record<string, any> = {};
  
  // Add any additional form fields
  if (formData) {
    Object.keys(formData).forEach(key => {
      if (!['name', 'customerName', 'email', 'mobile', 'customerMobile', 'phone', 
            'address', 'city', 'pincode', 'zipcode', 'landArea', 'soilType', 
            'district', 'taluk'].includes(key)) {
        additionalData[key] = formData[key];
      }
    });
  }

  // Add comprehensive payment-specific data
  if (paymentData?.bank_ref_num) {
    additionalData.bankReferenceNumber = paymentData.bank_ref_num;
  }

  if (paymentData?.bankcode) {
    additionalData.bankCode = paymentData.bankcode;
  }

  if (paymentData?.bank_name) {
    additionalData.bankName = paymentData.bank_name;
  }

  if (paymentData?.cardnum) {
    additionalData.cardNumber = paymentData.cardnum;
  }

  if (paymentData?.cardhash) {
    additionalData.cardHash = paymentData.cardhash;
  }

  // Add UPI details if available
  if (paymentData?.upi_va) {
    additionalData.upiVirtualAddress = paymentData.upi_va;
  }

  // Add comprehensive timestamp information
  if (paymentData?.timestamp) {
    additionalData.paymentTimestamp = paymentData.timestamp;
  }

  if (paymentData?.addedon) {
    additionalData.addedOn = paymentData.addedon;
  }

  // Add verification status
  if (transactionDetails?.hash_verified !== undefined) {
    additionalData.hashVerified = transactionDetails.hash_verified;
  }

  // Add PayU response data for debugging
  if (paymentData?.error) {
    additionalData.error = paymentData.error;
  }

  if (paymentData?.error_Message) {
    additionalData.errorMessage = paymentData.error_Message;
  }

  // Add payment gateway specific data
  if (paymentData?.pg_type) {
    additionalData.paymentGatewayType = paymentData.pg_type;
  }

  if (paymentData?.discount) {
    additionalData.discount = paymentData.discount;
  }

  if (paymentData?.net_amount_debit) {
    additionalData.netAmountDebit = paymentData.net_amount_debit;
  }

  const receiptData: ReceiptData = {
    // Transaction Details
    transactionId,
    paymentId,
    paymentDate,
    paymentMethod,
    status,
    clientId, // Add the generated client ID

    // PayU Specific Transaction Details
    mihpayid: mihpayid || undefined,
    bankReferenceNumber: bankReferenceNumber || undefined,
    paymentGateway: paymentGateway || undefined,
    paymentMode: paymentMode || undefined,

    // Amount Breakdown
    baseAmount,
    gstAmount,
    platformFeeAmount,
    totalAmount,

    // Customer Information
    customerName: customerName.trim(),
    customerEmail: customerEmail.trim(),
    customerPhone: customerPhone.trim(),
    customerAddress: customerAddress.trim() || undefined,
    customerCity: customerCity.trim() || undefined,
    customerPincode: customerPincode.trim() || undefined,

    // Registration Details
    landArea: landArea.trim() || undefined,
    soilType: soilType.trim() || undefined,
    district: district.trim() || undefined,
    taluk: taluk.trim() || undefined,

    // Additional Data
    additionalData
  };

  // Store client ID for tracking
  storeClientId({
    clientId,
    transactionId,
    customerName: customerName.trim(),
    customerEmail: customerEmail.trim(),
    paymentAmount: totalAmount,
    createdAt: new Date().toISOString()
  });

  return receiptData;
}

/**
 * Format payment method for display
 */
function formatPaymentMethod(method: string): string {
  if (!method) return 'PayU';
  
  const methodMap: Record<string, string> = {
    'CC': 'Credit Card',
    'DC': 'Debit Card',
    'NB': 'Net Banking',
    'UPI': 'UPI',
    'WALLET': 'Digital Wallet',
    'EMI': 'EMI',
    'CARDLESSEMI': 'Cardless EMI',
    'creditcard': 'Credit Card',
    'debitcard': 'Debit Card',
    'netbanking': 'Net Banking',
    'upi': 'UPI',
    'wallet': 'Digital Wallet',
    'emi': 'EMI',
    'cardlessemi': 'Cardless EMI'
  };

  return methodMap[method.toUpperCase()] || 
         methodMap[method.toLowerCase()] || 
         method.charAt(0).toUpperCase() + method.slice(1).toLowerCase();
}

/**
 * Format status for display
 */
function formatStatus(status: string): string {
  if (!status) return 'Unknown';

  const statusMap: Record<string, string> = {
    'success': 'Success',
    'failure': 'Failed',
    'pending': 'Pending',
    'cancelled': 'Cancelled',
    'refunded': 'Refunded'
  };

  return statusMap[status.toLowerCase()] ||
         status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
}

/**
 * Validate receipt data before generation
 */
export function validateReceiptData(receiptData: ReceiptData): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Required fields validation
  if (!receiptData.transactionId) {
    errors.push('Transaction ID is required');
  }

  if (!receiptData.customerName) {
    errors.push('Customer name is required');
  }

  if (!receiptData.customerEmail) {
    errors.push('Customer email is required');
  }

  if (!receiptData.paymentDate) {
    errors.push('Payment date is required');
  }

  if (!receiptData.clientId) {
    errors.push('Client ID is required');
  }

  // Amount validation
  if (receiptData.totalAmount <= 0) {
    errors.push('Total amount must be greater than 0');
  }

  if (receiptData.baseAmount <= 0) {
    errors.push('Base amount must be greater than 0');
  }

  // Email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (receiptData.customerEmail && !emailRegex.test(receiptData.customerEmail)) {
    errors.push('Invalid email format');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Create a summary of the receipt for logging
 */
export function createReceiptSummary(receiptData: ReceiptData): string {
  return `Receipt Summary:
- Client ID: ${receiptData.clientId}
- Transaction ID: ${receiptData.transactionId}
- Customer: ${receiptData.customerName}
- Amount: ₹${receiptData.totalAmount}
- Date: ${new Date(receiptData.paymentDate).toLocaleDateString('en-IN')}
- Status: ${receiptData.status}`;
}

const receiptService = {
  generateClientId,
  generateReceiptId,
  createReceiptData,
  validateReceiptData,
  createReceiptSummary
};

export default receiptService;
