import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Grid,
  TextField,
  Switch,
  FormControlLabel,
  Button,
  Alert,
  Di<PERSON>r,
  Card,
  CardContent,
  CardHeader
} from '@mui/material';
import { useCMS } from '../contexts/CMSContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`cms-tabpanel-${index}`}
      aria-labelledby={`cms-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const CMSAdmin: React.FC = () => {
  const { cmsSettings, paymentSettings, receiptSettings, uiContent, featureFlags, loading, error } = useCMS();
  const [activeTab, setActiveTab] = useState(0);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleSave = async () => {
    setSaveStatus('saving');
    try {
      // In a real implementation, this would save to your CMS backend
      // For now, we'll just simulate a save
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (err) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading CMS content...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">Error loading CMS content: {error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Content Management System
      </Typography>
      
      {saveStatus === 'saved' && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Content saved successfully!
        </Alert>
      )}
      
      {saveStatus === 'error' && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Failed to save content. Please try again.
        </Alert>
      )}

      <Paper sx={{ width: '100%' }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="CMS tabs">
          <Tab label="Site Settings" />
          <Tab label="Payment Settings" />
          <Tab label="Receipt Settings" />
          <Tab label="UI Content" />
          <Tab label="Feature Flags" />
        </Tabs>

        {/* Site Settings Tab */}
        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Site Information</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Site Title"
                value={cmsSettings?.site.title || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Site URL"
                value={cmsSettings?.site.url || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Site Description"
                value={cmsSettings?.site.description || ''}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Contact Information</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={cmsSettings?.contact.phone || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email Address"
                value={cmsSettings?.contact.email || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Address"
                value={cmsSettings?.contact.address || ''}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Payment Settings Tab */}
        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Registration Fee</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label="Base Amount"
                value={paymentSettings?.registration.baseAmount || 0}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label="GST Percentage"
                value={paymentSettings?.registration.gstPercentage || 0}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                type="number"
                label="Total Amount"
                value={paymentSettings?.registration.totalAmount || 0}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Payment Methods</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={<Switch checked={paymentSettings?.methods.creditCards || false} />}
                label="Enable Credit Cards"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={<Switch checked={paymentSettings?.methods.debitCards || false} />}
                label="Enable Debit Cards"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={<Switch checked={paymentSettings?.methods.netBanking || false} />}
                label="Enable Net Banking"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={<Switch checked={paymentSettings?.methods.upi || false} />}
                label="Enable UPI"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* Receipt Settings Tab */}
        <TabPanel value={activeTab} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Receipt Header</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Company Name"
                value={receiptSettings?.header.companyName || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Receipt Title"
                value={receiptSettings?.header.title || ''}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Receipt Footer</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Thank You Message"
                value={receiptSettings?.footer.thankYou || ''}
                variant="outlined"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Tagline"
                value={receiptSettings?.footer.tagline || ''}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* UI Content Tab */}
        <TabPanel value={activeTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Button Text" />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Submit Button"
                        value={uiContent?.buttons.primary.submit || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Register Button"
                        value={uiContent?.buttons.primary.register || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Contact Button"
                        value={uiContent?.buttons.primary.contact || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Messages" />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Form Submitted"
                        value={uiContent?.messages.success.formSubmitted || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Payment Success"
                        value={uiContent?.messages.success.paymentSuccess || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Payment Error"
                        value={uiContent?.messages.error.paymentError || ''}
                        variant="outlined"
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Feature Flags Tab */}
        <TabPanel value={activeTab} index={4}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Payment Features" />
                <CardContent>
                  <FormControlLabel
                    control={<Switch checked={featureFlags?.payment.enablePayment || false} />}
                    label="Enable Payment Gateway"
                  />
                  <br />
                  <FormControlLabel
                    control={<Switch checked={featureFlags?.payment.enableReceipt || false} />}
                    label="Enable Receipt Download"
                  />
                  <br />
                  <FormControlLabel
                    control={<Switch checked={featureFlags?.payment.enableEmailNotifications || false} />}
                    label="Enable Email Notifications"
                  />
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="UI Features" />
                <CardContent>
                  <FormControlLabel
                    control={<Switch checked={featureFlags?.ui.enableAnimations || false} />}
                    label="Enable Animations"
                  />
                  <br />
                  <FormControlLabel
                    control={<Switch checked={featureFlags?.ui.enableLoadingStates || false} />}
                    label="Enable Loading States"
                  />
                  <br />
                  <FormControlLabel
                    control={<Switch checked={featureFlags?.ui.enableTooltips || false} />}
                    label="Enable Tooltips"
                  />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <Box sx={{ p: 3, borderTop: 1, borderColor: 'divider' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSave}
            disabled={saveStatus === 'saving'}
          >
            {saveStatus === 'saving' ? 'Saving...' : 'Save Changes'}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default CMSAdmin;
