import React, { useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,

  Alert
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Home as HomeIcon,
  Receipt as ReceiptIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

const RegistrationSuccess: React.FC = () => {
  const navigate = useNavigate();

  const { language } = useLanguage();

  // Get registration details from URL parameters or localStorage
  const registrationData = localStorage.getItem('registrationData');
  const parsedData = registrationData ? JSON.parse(registrationData) : null;

  // Translations
  const translations = {
    en: {
      title: 'Registration Submitted!',
      subtitle: 'Your registration request has been submitted successfully',
      registrationId: 'Registration ID',
      service: 'Service',
      nextSteps: 'What happens next?',
      step1: 'Our team will review your registration details',
      step2: 'You will receive a confirmation call within 24-48 hours',
      step3: 'Our agricultural experts will schedule a site visit',
      contact: 'For any queries, contact us at',
      goHome: 'Go to Home',
      contactUs: 'Contact Us',
      registrationFailed: 'Registration submission failed',
      tryAgain: 'Please try again or contact support'
    },
    kn: {
      title: 'ನೋಂದಣಿ ಸಲ್ಲಿಸಲಾಗಿದೆ!',
      subtitle: 'ನಿಮ್ಮ ನೋಂದಣಿ ವಿನಂತಿಯನ್ನು ಯಶಸ್ವಿಯಾಗಿ ಸಲ್ಲಿಸಲಾಗಿದೆ',
      registrationId: 'ನೋಂದಣಿ ಐಡಿ',
      service: 'ಸೇವೆ',
      nextSteps: 'ಮುಂದೆ ಏನಾಗುತ್ತದೆ?',
      step1: 'ನಮ್ಮ ತಂಡವು ನಿಮ್ಮ ನೋಂದಣಿ ವಿವರಗಳನ್ನು ಪರಿಶೀಲಿಸುತ್ತದೆ',
      step2: '24-48 ಗಂಟೆಗಳಲ್ಲಿ ನೀವು ದೃಢೀಕರಣ ಕರೆ ಸ್ವೀಕರಿಸುತ್ತೀರಿ',
      step3: 'ನಮ್ಮ ಕೃಷಿ ತಜ್ಞರು ಸೈಟ್ ಭೇಟಿಯನ್ನು ನಿಗದಿಪಡಿಸುತ್ತಾರೆ',
      contact: 'ಯಾವುದೇ ಪ್ರಶ್ನೆಗಳಿಗಾಗಿ, ನಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸಿ',
      goHome: 'ಮುಖ್ಯ ಪುಟಕ್ಕೆ ಹೋಗಿ',
      contactUs: 'ನಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸಿ',
      registrationFailed: 'ನೋಂದಣಿ ಸಲ್ಲಿಕೆ ವಿಫಲವಾಗಿದೆ',
      tryAgain: 'ದಯವಿಟ್ಟು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ ಅಥವಾ ಬೆಂಬಲವನ್ನು ಸಂಪರ್ಕಿಸಿ'
    },
    hi: {
      title: 'पंजीकरण जमा किया गया!',
      subtitle: 'आपका पंजीकरण अनुरोध सफलतापूर्वक जमा किया गया है',
      registrationId: 'पंजीकरण आईडी',
      service: 'सेवा',
      nextSteps: 'आगे क्या होता है?',
      step1: 'हमारी टीम आपके पंजीकरण विवरण की समीक्षा करेगी',
      step2: 'आपको 24-48 घंटों के भीतर एक पुष्टिकरण कॉल प्राप्त होगी',
      step3: 'हमारे कृषि विशेषज्ञ साइट विजिट शेड्यूल करेंगे',
      contact: 'किसी भी प्रश्न के लिए, हमसे संपर्क करें',
      goHome: 'होम पर जाएं',
      contactUs: 'हमसे संपर्क करें',
      registrationFailed: 'पंजीकरण जमा करना विफल',
      tryAgain: 'कृपया पुनः प्रयास करें या सहायता से संपर्क करें'
    }
  };

  const t = translations[language as keyof typeof translations] || translations.en;

  useEffect(() => {
    // Clear registration data after showing success
    const timer = setTimeout(() => {
      localStorage.removeItem('registrationData');
    }, 5000); // Clear after 5 seconds

    return () => clearTimeout(timer);
  }, []);

  const handleGoHome = () => {
    navigate('/');
  };

  const handleContactUs = () => {
    navigate('/contact');
  };

  // Generate a simple registration ID based on timestamp
  const registrationId = parsedData?.timestamp
    ? `REG${new Date(parsedData.timestamp).getTime().toString().slice(-8)}`
    : `REG${Date.now().toString().slice(-8)}`;

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Paper elevation={3} sx={{ p: 6 }}>
        {/* Success Header */}
        <Box textAlign="center" mb={4}>
          <CheckCircleIcon
            sx={{
              fontSize: 80,
              color: 'success.main',
              mb: 2
            }}
          />
          <Typography variant="h3" gutterBottom color="success.main">
            {t.title}
          </Typography>
          <Typography variant="h6" color="textSecondary">
            {t.subtitle}
          </Typography>
        </Box>

        {/* Registration Details */}
        <Box mb={4}>
          <Typography variant="h6" gutterBottom>
            Registration Details
          </Typography>
          <Box sx={{ bgcolor: 'grey.50', p: 3, borderRadius: 2 }}>
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="body2" color="textSecondary">
                {t.registrationId}:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {registrationId}
              </Typography>
            </Box>
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="body2" color="textSecondary">
                {t.service}:
              </Typography>
              <Typography variant="body2" fontWeight="medium">
                {parsedData?.service || 'Darvi Group Farmer Registration'}
              </Typography>
            </Box>
            {parsedData?.customerName && (
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="textSecondary">
                  Name:
                </Typography>
                <Typography variant="body2" fontWeight="medium">
                  {parsedData.customerName}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>

        {/* Next Steps */}
        <Box mb={4}>
          <Typography variant="h6" gutterBottom>
            {t.nextSteps}
          </Typography>
          <Box component="ol" sx={{ pl: 2 }}>
            <Typography component="li" variant="body1" paragraph>
              {t.step1}
            </Typography>
            <Typography component="li" variant="body1" paragraph>
              {t.step2}
            </Typography>
            <Typography component="li" variant="body1" paragraph>
              {t.step3}
            </Typography>
          </Box>
        </Box>

        {/* Contact Information */}
        <Alert severity="info" sx={{ mb: 4 }}>
          <Typography variant="body1">
            {t.contact}: <strong>+91 9986890777</strong>
          </Typography>
        </Alert>

        {/* Action Buttons */}
        <Box display="flex" gap={2} justifyContent="center" flexWrap="wrap">
          <Button
            variant="contained"
            color="primary"
            onClick={handleGoHome}
            startIcon={<HomeIcon />}
          >
            {t.goHome}
          </Button>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleContactUs}
            startIcon={<ReceiptIcon />}
          >
            {t.contactUs}
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default RegistrationSuccess;
