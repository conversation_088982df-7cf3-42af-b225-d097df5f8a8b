# PayU Payment Gateway Integration - Netlify Functions

This directory contains the PayU payment gateway integration converted from the working PHP implementation in the `payu/` folder to JavaScript Netlify Functions.

## 🚀 Features

- **Secure Hash Generation**: SHA-512 hash generation following PayU documentation
- **Modular Architecture**: Separate utilities for configuration, validation, and logging
- **Environment-based Configuration**: Production-ready with environment variables
- **Comprehensive Logging**: Step-by-step debugging and transaction logging
- **CORS Security**: Proper CORS headers and security measures
- **Error Handling**: Robust error handling with detailed logging

## 📁 File Structure

```
netlify/functions/
├── utils/
│   ├── payuConfig.js      # PayU configuration and environment handling
│   ├── hashGenerator.js   # Hash generation and verification utilities
│   ├── payuValidator.js   # Input validation and sanitization
│   └── logger.js          # Transaction logging and debugging
├── payu-initiate.js       # Payment initiation endpoint
├── payu-success.js        # Payment success handler
├── payu-failure.js        # Payment failure handler
├── payu-verify.js         # Payment verification endpoint
├── health.js              # Health check endpoint
├── package.json           # Dependencies
└── README.md              # This file
```

## 🔧 Environment Variables

Required environment variables in `.env`:

```env
# PayU Merchant Configuration
PAYU_MERCHANT_KEY=kCDLd6
PAYU_MERCHANT_ID=13017075
PAYU_SALT_32=ELd0paVbDhHaDfzpr/B2GBROiRZxoTj

# PayU Client Credentials
PAYU_CLIENT_ID=5f0458fc94005072bb08f32fb3427dd099ccb9a0acff22fe74a2e67bde87cf83
PAYU_CLIENT_SECRET=eeb1ce03b4fcba1a9a974a1eae71ec076710bc4f16cd41509acd6dd92aedbc21

# PayU URLs
PAYU_BASE_URL=https://secure.payu.in
PAYU_PAYMENT_URL=https://secure.payu.in/_payment
PAYU_TEST_MODE=false

# Application Environment
NODE_ENV=development
```

## 🛠️ API Endpoints

### 1. Health Check
- **Endpoint**: `/.netlify/functions/health`
- **Method**: GET
- **Purpose**: Check system health and configuration

### 2. Payment Initiation
- **Endpoint**: `/.netlify/functions/payu-initiate`
- **Method**: POST
- **Purpose**: Initialize payment and generate hash

**Request Body:**
```json
{
  "txnid": "TXN123456789",
  "amount": "3.00",
  "productinfo": "Test Payment",
  "firstname": "John",
  "email": "<EMAIL>",
  "phone": "**********",
  "address1": "Test Address",
  "city": "Bangalore",
  "state": "Karnataka",
  "country": "India",
  "zipcode": "560001"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment initiated successfully",
  "data": {
    "txnid": "TXN123456789",
    "hash": "generated_hash_here",
    "key": "merchant_key",
    "amount": "3.00",
    "paymentUrl": "https://secure.payu.in/_payment",
    "paymentData": { /* complete payment data */ }
  }
}
```

### 3. Payment Success Handler
- **Endpoint**: `/.netlify/functions/payu-success`
- **Method**: POST/GET
- **Purpose**: Handle successful payment responses from PayU

### 4. Payment Failure Handler
- **Endpoint**: `/.netlify/functions/payu-failure`
- **Method**: POST/GET
- **Purpose**: Handle failed payment responses from PayU

### 5. Payment Verification
- **Endpoint**: `/.netlify/functions/payu-verify`
- **Method**: POST
- **Purpose**: Verify payment status with PayU API

## 🔐 Security Features

1. **Hash Verification**: All PayU responses are verified using reverse hash
2. **CORS Protection**: Restricted origins based on environment
3. **Input Sanitization**: All inputs are validated and sanitized
4. **Environment Isolation**: Separate configurations for development/production
5. **Sensitive Data Masking**: Logs mask sensitive information

## 🧪 Testing

Run the test suite to verify the integration:

```bash
# Start Netlify functions server
netlify functions:serve

# Run tests (in another terminal)
node test-payu-functions.js
```

## 📝 Hash Generation

PayU hash is generated using the following format:
```
sha512(key|txnid|amount|productinfo|firstname|email|udf1|udf2|udf3|udf4|udf5||||||SALT)
```

Reverse hash for verification:
```
sha512(SALT|status||||||udf5|udf4|udf3|udf2|udf1|email|firstname|productinfo|amount|txnid|key)
```

## 🚀 Deployment

1. **Environment Variables**: Set all required environment variables in Netlify dashboard
2. **Build Settings**: Ensure `netlify.toml` is configured correctly
3. **Function Deployment**: Functions are automatically deployed with the site

## 🔍 Debugging

Enable debug logging by setting `NODE_ENV=development`. This will show:
- Hash generation details
- Request/response data
- Step-by-step processing logs
- PayU API interactions

## 📞 Support

For issues or questions:
- Check the health endpoint: `/.netlify/functions/health`
- Review function logs in Netlify dashboard
- Verify environment variables are set correctly
- Ensure PayU credentials are valid

## 🔄 Migration from PHP

This implementation replaces the PHP files:
- `payu/config.php` → `utils/payuConfig.js`
- `payu/process_payment.php` → `payu-initiate.js`
- `payu/success.php` → `payu-success.js`
- `payu/failure.php` → `payu-failure.js`
- `payu/verify_payment.php` → `payu-verify.js`

All functionality has been preserved and enhanced with better error handling and logging.
