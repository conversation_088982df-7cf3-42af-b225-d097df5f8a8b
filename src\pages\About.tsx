import React from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Box,
  Divider,
} from '@mui/material';
import EmojiNatureIcon from '@mui/icons-material/EmojiNature';
import GroupsIcon from '@mui/icons-material/Groups';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import SchoolIcon from '@mui/icons-material/School';
import WorkIcon from '@mui/icons-material/Work';
import StarIcon from '@mui/icons-material/Star';

// Import content data
import teamContent from '../content/home/<USER>';
import aboutContent from '../content/about/content.json';

const About = () => {
  // Process values from JSON content
  const values = aboutContent.values.map(value => {
    // Map icon string to actual icon component
    let iconComponent;
    switch (value.icon) {
      case 'EmojiNature':
        iconComponent = <EmojiNatureIcon sx={{ fontSize: 40 }} />;
        break;
      case 'Groups':
        iconComponent = <GroupsIcon sx={{ fontSize: 40 }} />;
        break;
      case 'Lightbulb':
        iconComponent = <LightbulbIcon sx={{ fontSize: 40 }} />;
        break;
      case 'TrendingUp':
        iconComponent = <TrendingUpIcon sx={{ fontSize: 40 }} />;
        break;
      default:
        iconComponent = <EmojiNatureIcon sx={{ fontSize: 40 }} />;
    }

    return {
      icon: iconComponent,
      title: value.title,
      description: value.description
    };
  });

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Hero Section */}
      <Box sx={{ mb: 6 }}>
        <Typography variant="h4" component="h1" gutterBottom align="center" color="primary">
          {aboutContent.hero.title}
        </Typography>
        <Typography variant="subtitle1" align="center" paragraph color="text.secondary">
          {aboutContent.hero.subtitle}
        </Typography>
      </Box>

      {/* Business Description */}
      <Card sx={{ mb: 6 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom color="primary">
            {aboutContent.business.title}
          </Typography>
          {aboutContent.business.content.map((paragraph, index) => (
            <Typography key={index} variant="body1" paragraph>
              {paragraph}
            </Typography>
          ))}
        </CardContent>
      </Card>

      {/* Mission Statement */}
      <Card sx={{ mb: 6 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom color="primary">
            {aboutContent.mission.title}
          </Typography>
          <Typography variant="body1" paragraph>
            {aboutContent.mission.content}
          </Typography>
        </CardContent>
      </Card>

      {/* Our Values */}
      <Typography variant="h5" gutterBottom align="center" color="primary" sx={{ mb: 4 }}>
        Our Values
      </Typography>
      <Grid container spacing={4}>
        {values.map((value, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textAlign: 'center',
              }}
            >
              <CardContent>
                <Box color="primary.main" mb={2}>
                  {value.icon}
                </Box>
                <Typography variant="h6" component="h3" gutterBottom>
                  {value.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {value.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Our Team */}
      <Box sx={{ mt: 6, mb: 6 }}>
        <Typography variant="h5" gutterBottom align="center" color="primary" sx={{ mb: 4 }}>
          Our Team
        </Typography>

        {teamContent.teamMembers.map((member, index) => (
          <Card key={index} sx={{ mb: 4, overflow: 'visible' }}>
            <CardContent sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} sm={3} md={2} sx={{ textAlign: 'center' }}>
                  <Box
                    sx={{
                      width: { xs: 120, sm: 140, md: 160 },
                      height: { xs: 120, sm: 140, md: 160 },
                      borderRadius: '50%',
                      overflow: 'hidden',
                      mx: 'auto',
                      mb: { xs: 2, sm: 0 },
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
                      border: '4px solid #4caf50',
                    }}
                  >
                    <img
                      src={member.image}
                      alt={member.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                      }}
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/darvi-logo.png'; // Fallback image
                      }}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} sm={9} md={10}>
                  <Typography variant="h5" color="primary" gutterBottom>
                    {member.name}
                  </Typography>

                  <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                    <strong>{member.position}</strong>
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <SchoolIcon sx={{ color: 'primary.main', mr: 1, fontSize: 20 }} />
                    <Typography variant="body2">
                      <strong>Education:</strong> {member.education}
                    </Typography>
                  </Box>

                  {member.specialization && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <StarIcon sx={{ color: 'primary.main', mr: 1, fontSize: 20 }} />
                      <Typography variant="body2">
                        <strong>Specialization:</strong> {member.specialization}
                      </Typography>
                    </Box>
                  )}

                  {member.experience && (
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                      <WorkIcon sx={{ color: 'primary.main', mr: 1, mt: 0.5, fontSize: 20 }} />
                      <Typography variant="body2">
                        <strong>Experience:</strong> {member.experience}
                      </Typography>
                    </Box>
                  )}

                  {member.currentProject && (
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
                      <LightbulbIcon sx={{ color: 'primary.main', mr: 1, mt: 0.5, fontSize: 20 }} />
                      <Typography variant="body2">
                        <strong>Current Project:</strong> {member.currentProject}
                      </Typography>
                    </Box>
                  )}

                  <Divider sx={{ my: 1.5 }} />

                  <Typography variant="body1" paragraph>
                    {member.fullDescription || member.shortDescription}
                  </Typography>

                  {member.background && (
                    <Typography variant="body1" paragraph>
                      {member.background}
                    </Typography>
                  )}

                  {member.mission && (
                    <Typography variant="body1" paragraph>
                      {member.mission}
                    </Typography>
                  )}
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        ))}
      </Box>

      {/* Company History */}
      <Box sx={{ mt: 6 }}>
        <Typography variant="h5" gutterBottom color="primary">
          {aboutContent.journey.title}
        </Typography>
        {aboutContent.journey.content.map((paragraph, index) => (
          <Typography key={index} variant="body1" paragraph>
            {paragraph}
          </Typography>
        ))}
      </Box>

      {/* Success Stories */}
      <Card sx={{ mt: 6 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom color="primary">
            {aboutContent.successStories.title}
          </Typography>

          {aboutContent.successStories.stories.map((story, index) => (
            <Box key={index} sx={{ mb: index < aboutContent.successStories.stories.length - 1 ? 3 : 0 }}>
              <Typography variant="h6" gutterBottom>
                {story.title}
              </Typography>
              <Typography variant="body1" paragraph>
                {story.description}
              </Typography>
            </Box>
          ))}
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card sx={{ mt: 6, mb: 4 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom color="primary">
            {aboutContent.contactInfo.title}
          </Typography>
          <Typography variant="body1" paragraph>
            <strong>Address:</strong> {aboutContent.contactInfo.address}
          </Typography>
          <Typography variant="body1" paragraph>
            <strong>Phone:</strong> {aboutContent.contactInfo.phone}
          </Typography>
          <Typography variant="body1" paragraph>
            <strong>Email:</strong> {aboutContent.contactInfo.email}
          </Typography>
          <Typography variant="body1" paragraph>
            <strong>Working Hours:</strong> {aboutContent.contactInfo.workingHours}
          </Typography>
        </CardContent>
      </Card>
    </Container>
  );
};

export default About;
