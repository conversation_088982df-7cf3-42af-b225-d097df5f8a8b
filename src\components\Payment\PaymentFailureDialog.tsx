import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert
} from '@mui/material';
import {
  Error as ErrorIcon,
  Phone as PhoneIcon
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';

interface PaymentFailureDialogProps {
  open: boolean;
  onClose: () => void;
  onRetry: () => void;
  error: string;
}

const PaymentFailureDialog: React.FC<PaymentFailureDialogProps> = ({
  open,
  onClose,
  onRetry,
  error
}) => {
  const { language } = useLanguage();

  const translations = {
    en: {
      title: 'Payment Failed',
      subtitle: 'We encountered an issue processing your payment',
      errorLabel: 'Error Details:',
      helpText: 'Don\'t worry! You can try again or contact us for assistance.',
      contactInfo: 'For immediate help, call us at:',
      phoneNumber: '+91 9986890777',
      tryAgain: 'Try Again',
      cancel: 'Cancel',
      reasons: {
        'Payment cancelled by user': 'Payment was cancelled',
        'Payment initiation failed': 'Unable to start payment process',
        'Something went wrong': 'Technical error occurred'
      }
    },
    hi: {
      title: 'भुगतान विफल',
      subtitle: 'आपके भुगतान को प्रोसेस करने में समस्या आई',
      errorLabel: 'त्रुटि विवरण:',
      helpText: 'चिंता न करें! आप फिर से कोशिश कर सकते हैं या सहायता के लिए हमसे संपर्क कर सकते हैं।',
      contactInfo: 'तत्काल सहायता के लिए हमें कॉल करें:',
      phoneNumber: '+91 9986890777',
      tryAgain: 'पुनः प्रयास करें',
      cancel: 'रद्द करें',
      reasons: {
        'Payment cancelled by user': 'भुगतान रद्द कर दिया गया',
        'Payment initiation failed': 'भुगतान प्रक्रिया शुरू नहीं हो सकी',
        'Something went wrong': 'तकनीकी त्रुटि हुई'
      }
    },
    kn: {
      title: 'ಪಾವತಿ ವಿಫಲವಾಗಿದೆ',
      subtitle: 'ನಿಮ್ಮ ಪಾವತಿಯನ್ನು ಪ್ರಕ್ರಿಯೆಗೊಳಿಸುವಲ್ಲಿ ಸಮಸ್ಯೆ ಎದುರಾಗಿದೆ',
      errorLabel: 'ದೋಷದ ವಿವರಗಳು:',
      helpText: 'ಚಿಂತಿಸಬೇಡಿ! ನೀವು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಬಹುದು ಅಥವಾ ಸಹಾಯಕ್ಕಾಗಿ ನಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸಬಹುದು।',
      contactInfo: 'ತಕ್ಷಣದ ಸಹಾಯಕ್ಕಾಗಿ ನಮಗೆ ಕರೆ ಮಾಡಿ:',
      phoneNumber: '+91 9986890777',
      tryAgain: 'ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ',
      cancel: 'ರದ್ದುಮಾಡಿ',
      reasons: {
        'Payment cancelled by user': 'ಪಾವತಿಯನ್ನು ರದ್ದುಗೊಳಿಸಲಾಗಿದೆ',
        'Payment initiation failed': 'ಪಾವತಿ ಪ್ರಕ್ರಿಯೆ ಪ್ರಾರಂಭಿಸಲು ಸಾಧ್ಯವಾಗಲಿಲ್ಲ',
        'Something went wrong': 'ತಾಂತ್ರಿಕ ದೋಷ ಸಂಭವಿಸಿದೆ'
      }
    }
  };

  const t = translations[language as keyof typeof translations];

  // Get localized error message
  const getLocalizedError = (errorMsg: string) => {
    const reasons = t.reasons as Record<string, string>;
    return reasons[errorMsg] || errorMsg;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          margin: { xs: 1, sm: 2 },
          maxHeight: { xs: '95vh', sm: '90vh' },
          borderRadius: { xs: '12px', sm: '16px' }
        }
      }}
    >
      <DialogTitle sx={{
        px: { xs: 2, sm: 3 },
        py: { xs: 2, sm: 2.5 }
      }}>
        <Box display="flex" alignItems="center" gap={{ xs: 1.5, sm: 2 }}>
          <ErrorIcon
            color="error"
            sx={{ fontSize: { xs: '2rem', sm: '2.25rem' } }}
          />
          <Box>
            <Typography
              variant="h6"
              color="error"
              gutterBottom
              sx={{
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                fontWeight: 600,
                lineHeight: 1.2
              }}
            >
              {t.title}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                lineHeight: 1.4
              }}
            >
              {t.subtitle}
            </Typography>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent sx={{
        px: { xs: 2, sm: 3 },
        py: { xs: 1, sm: 2 }
      }}>
        {/* Error Details */}
        <Alert
          severity="error"
          sx={{
            mb: { xs: 1.5, sm: 2 },
            fontSize: { xs: '0.875rem', sm: '1rem' }
          }}
        >
          <Typography
            variant="body2"
            gutterBottom
            sx={{
              fontSize: { xs: '0.875rem', sm: '1rem' },
              lineHeight: 1.4
            }}
          >
            <strong>{t.errorLabel}</strong>
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: '0.875rem', sm: '1rem' },
              lineHeight: 1.4
            }}
          >
            {getLocalizedError(error)}
          </Typography>
        </Alert>

        {/* Help Text */}
        <Typography
          variant="body2"
          color="text.secondary"
          paragraph
          sx={{
            fontSize: { xs: '0.875rem', sm: '1rem' },
            lineHeight: 1.5,
            mb: { xs: 1.5, sm: 2 }
          }}
        >
          {t.helpText}
        </Typography>

        {/* Contact Information */}
        <Box
          sx={{
            p: { xs: 1.5, sm: 2 },
            bgcolor: 'primary.light',
            borderRadius: { xs: '8px', sm: '12px' },
            display: 'flex',
            alignItems: 'center',
            gap: { xs: 1, sm: 1.5 },
            flexDirection: { xs: 'column', sm: 'row' },
            textAlign: { xs: 'center', sm: 'left' }
          }}
        >
          <PhoneIcon
            color="primary"
            sx={{ fontSize: { xs: '1.5rem', sm: '1.75rem' } }}
          />
          <Box>
            <Typography
              variant="body2"
              color="primary.dark"
              sx={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                lineHeight: 1.4
              }}
            >
              {t.contactInfo}
            </Typography>
            <Typography
              variant="h6"
              color="primary.dark"
              fontWeight="bold"
              sx={{
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                lineHeight: 1.2
              }}
            >
              {t.phoneNumber}
            </Typography>
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{
        p: { xs: 2, sm: 3 },
        gap: { xs: 1, sm: 1.5 },
        flexDirection: { xs: 'column', sm: 'row' }
      }}>
        <Button
          onClick={onClose}
          variant="outlined"
          color="inherit"
          sx={{
            minHeight: { xs: '48px', sm: '44px' },
            fontSize: { xs: '1rem', sm: '0.875rem' },
            order: { xs: 2, sm: 1 },
            width: { xs: '100%', sm: 'auto' }
          }}
        >
          {t.cancel}
        </Button>
        <Button
          onClick={onRetry}
          variant="contained"
          color="primary"
          size="large"
          sx={{
            minHeight: { xs: '48px', sm: '44px' },
            fontSize: { xs: '1rem', sm: '0.875rem' },
            fontWeight: 600,
            order: { xs: 1, sm: 2 },
            width: { xs: '100%', sm: 'auto' }
          }}
        >
          {t.tryAgain}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentFailureDialog;
