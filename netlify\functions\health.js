/**
 * Health Check Function
 * Simple health check endpoint for monitoring
 */

const { validateEnvironment } = require('./utils/payuConfig');

exports.handler = async (event) => {
  // CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Check environment configuration
    const envValidation = validateEnvironment();
    
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      payu_config: {
        merchant_key_configured: !!process.env.PAYU_MERCHANT_KEY,
        merchant_id_configured: !!process.env.PAYU_MERCHANT_ID,
        salt_configured: !!(process.env.PAYU_SALT_256 || process.env.PAYU_SALT_32),
        environment_valid: envValidation.isValid
      },
      functions: {
        payu_initiate: 'available',
        payu_success: 'available',
        payu_failure: 'available',
        payu_verify: 'available'
      }
    };

    if (!envValidation.isValid) {
      healthStatus.status = 'degraded';
      healthStatus.config_errors = envValidation.errors;
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify(healthStatus)
    };

  } catch (error) {
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message
      })
    };
  }
};
