import React, { useState } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  Divider,
  List,
  ListItem,
  ListItemText,
  Paper,
  Alert,
  CircularProgress
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import SecurityIcon from '@mui/icons-material/Security';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import darviData from '../data/darviContentData.json';

const Checkout = () => {

  const navigate = useNavigate();
  const { registrationServices } = darviData;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const [formData, setFormData] = useState({
    customerName: '',
    customerMobile: '',
    email: '',
    city: '',
    district: ''
  });

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
    setError('');
  };

  const validateForm = () => {
    if (!formData.customerName.trim()) {
      setError('Please enter your full name');
      return false;
    }
    if (!formData.customerMobile.trim()) {
      setError('Please enter your mobile number');
      return false;
    }
    if (!/^[6-9]\d{9}$/.test(formData.customerMobile)) {
      setError('Please enter a valid 10-digit mobile number');
      return false;
    }
    if (!formData.email.trim()) {
      setError('Please enter your email address');
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }
    if (!formData.city.trim()) {
      setError('Please enter your city');
      return false;
    }
    if (!formData.district.trim()) {
      setError('Please enter your district');
      return false;
    }
    return true;
  };

  const handleRegistration = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError('');

    try {
      // Store registration data for contact
      localStorage.setItem('registrationData', JSON.stringify({
        ...formData,
        service: 'Darvi Group Farmer Registration',
        amount: registrationServices.registrationFee.amount,
        timestamp: new Date().toISOString()
      }));

      // Navigate to contact page for further assistance
      navigate('/contact?registration=true');
    } catch (err) {
      setError('Something went wrong. Please try again later.');
      // Registration error occurred
    } finally {
      setLoading(false);
    }
  };

  const orderSummary = {
    item: 'Darvi Group Farmer Registration',
    price: registrationServices.registrationFee.amount,
    description: registrationServices.registrationFee.description
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom color="primary">
          <ShoppingCartIcon sx={{ mr: 2, fontSize: 'inherit' }} />
          Checkout
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Complete your registration
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* Order Summary */}
        <Grid item xs={12} md={5}>
          <Card elevation={3} sx={{ borderRadius: '16px', position: 'sticky', top: 20 }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom fontWeight={600}>
                Order Summary
              </Typography>

              <Box sx={{ my: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body1" fontWeight={500}>
                    {orderSummary.item}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {orderSummary.description}
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body1">Subtotal:</Typography>
                  <Typography variant="body1">₹{orderSummary.price.toLocaleString()}</Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body1">Processing Fee:</Typography>
                  <Typography variant="body1" color="success.main">Free</Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="h6" fontWeight={600}>Total:</Typography>
                  <Typography variant="h6" fontWeight={600} color="primary">
                    ₹{orderSummary.price.toLocaleString()}
                  </Typography>
                </Box>
              </Box>

              {/* What's Included */}
              <Paper elevation={1} sx={{ p: 2, backgroundColor: '#F8F9FA', borderRadius: '12px' }}>
                <Typography variant="subtitle2" gutterBottom fontWeight={600}>
                  What's Included:
                </Typography>
                <List dense sx={{ p: 0 }}>
                  {registrationServices.benefits.slice(0, 4).map((benefit, index) => (
                    <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                      <CheckCircleIcon sx={{ fontSize: 16, color: 'success.main', mr: 1 }} />
                      <ListItemText
                        primary={benefit.title}
                        primaryTypographyProps={{ fontSize: '0.85rem' }}
                      />
                    </ListItem>
                  ))}
                  <ListItem sx={{ px: 0, py: 0.5 }}>
                    <Typography variant="caption" color="text.secondary">
                      + 4 more benefits included
                    </Typography>
                  </ListItem>
                </List>
              </Paper>
            </CardContent>
          </Card>
        </Grid>

        {/* Registration Form */}
        <Grid item xs={12} md={7}>
          <Card elevation={3} sx={{ borderRadius: '16px' }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h5" gutterBottom fontWeight={600}>
                Registration Information
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 3, borderRadius: '8px' }}>
                  {error}
                </Alert>
              )}

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Full Name"
                    value={formData.customerName}
                    onChange={handleInputChange('customerName')}
                    required
                    variant="outlined"
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px' } }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Mobile Number"
                    value={formData.customerMobile}
                    onChange={handleInputChange('customerMobile')}
                    required
                    variant="outlined"
                    placeholder="10-digit mobile number"
                    inputProps={{ maxLength: 10 }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px' } }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Email Address"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    required
                    type="email"
                    variant="outlined"
                    placeholder="<EMAIL>"
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px' } }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="City"
                    value={formData.city}
                    onChange={handleInputChange('city')}
                    required
                    variant="outlined"
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px' } }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="District"
                    value={formData.district}
                    onChange={handleInputChange('district')}
                    required
                    variant="outlined"
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: '8px' } }}
                  />
                </Grid>
              </Grid>

              {/* Security Notice */}
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  mt: 3,
                  backgroundColor: '#E8F5E8',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <SecurityIcon sx={{ color: 'success.main', mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  Your information is secure and will be used only for registration purposes
                </Typography>
              </Paper>

              {/* Registration Button */}
              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleRegistration}
                disabled={loading}
                sx={{
                  mt: 4,
                  py: 2,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: '12px',
                  background: 'linear-gradient(45deg, #1B4C35 30%, #2E7D32 90%)',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #0A3622 30%, #1B4C35 90%)',
                  }
                }}
              >
                {loading ? (
                  <>
                    <CircularProgress size={24} sx={{ mr: 2, color: 'white' }} />
                    Processing...
                  </>
                ) : (
                  'Proceed with Registration - Contact Us'
                )}
              </Button>

              <Typography variant="caption" display="block" textAlign="center" sx={{ mt: 2 }} color="text.secondary">
                By proceeding with registration, you agree to our Terms of Service and Privacy Policy
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Checkout;
