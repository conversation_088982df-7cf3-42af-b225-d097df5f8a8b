/**
 * PayU API Client
 * Handles all PayU API communications with proper error handling and logging
 */

const { generatePaymentHash, verifyResponseHash } = require('./hashGenerator');
const { validatePaymentRequest, sanitizePaymentParams } = require('./payuValidator');

class PayUApiClient {
  constructor(config) {
    this.config = {
      key: config.key,
      salt: config.salt,
      mid: config.mid,
      apiUrl: config.apiUrl || 'https://test.payu.in/_payment',
      environment: config.environment || 'test'
    };
    
    this.validateConfig();
  }

  /**
   * Validate PayU configuration
   */
  validateConfig() {
    if (!this.config.key || !this.config.salt) {
      throw new Error('PayU key and salt are required');
    }
    
    if (this.config.environment === 'production' && this.config.apiUrl.includes('test')) {
      // Warning: Using test URL in production environment
    }
  }

  /**
   * Prepare payment request for PayU Hosted Checkout
   * @param {Object} paymentData - Payment parameters
   * @returns {Object} Prepared payment request
   */
  preparePaymentRequest(paymentData) {
    // Step 1: Validate input parameters
    const validation = validatePaymentRequest(paymentData);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Step 2: Sanitize parameters
    const sanitized = sanitizePaymentParams(paymentData);

    // Step 3: Prepare PayU payment data
    const payuData = {
      key: this.config.key,
      txnid: sanitized.txnid,
      amount: sanitized.amount,
      productinfo: sanitized.productinfo,
      firstname: sanitized.firstname,
      email: sanitized.email,
      phone: sanitized.phone,
      
      // Success and failure URLs
      surl: paymentData.surl || this.getDefaultSuccessUrl(),
      furl: paymentData.furl || this.getDefaultFailureUrl(),
      
      // Service provider
      service_provider: 'payu_paisa',
      
      // Optional address fields
      address1: sanitized.address1,
      city: sanitized.city,
      state: sanitized.state || 'Karnataka',
      country: sanitized.country || 'India',
      zipcode: sanitized.zipcode,
      
      // User defined fields
      udf1: sanitized.udf1,
      udf2: sanitized.udf2,
      udf3: sanitized.udf3,
      udf4: sanitized.udf4,
      udf5: sanitized.udf5,
      
      // PayU Hosted Checkout specific parameters
      enforce_paymethod: 'creditcard|debitcard|netbanking|upi|wallet|emi|cardlessemi',
      pg_language: 'en',
      
      // Add MID if available
      ...(this.config.mid && { mid: this.config.mid })
    };

    // Step 4: Generate hash
    try {
      payuData.hash = generatePaymentHash(payuData, this.config.salt);
    } catch (error) {
      throw new Error(`Hash generation failed: ${error.message}`);
    }

    // Step 5: Log request details (without sensitive data)
    this.logPaymentRequest(payuData);

    return {
      paymentData: payuData,
      paymentUrl: this.config.apiUrl
    };
  }

  /**
   * Verify PayU response
   * @param {Object} response - PayU response data
   * @returns {Object} Verification result
   */
  verifyPaymentResponse(response) {
    try {
      // Step 1: Basic response validation
      if (!response || !response.hash) {
        return {
          isValid: false,
          error: 'Invalid response: missing hash'
        };
      }

      // Step 2: Verify hash
      const isHashValid = verifyResponseHash(response, this.config.salt);
      
      // Step 3: Additional validations
      const validations = this.performAdditionalValidations(response);
      
      // Step 4: Log verification result
      this.logResponseVerification(response, isHashValid);

      return {
        isValid: isHashValid && validations.isValid,
        error: !isHashValid ? 'Hash verification failed' : validations.error,
        response: response
      };
    } catch (error) {
      // Response verification error
      return {
        isValid: false,
        error: `Verification error: ${error.message}`
      };
    }
  }

  /**
   * Perform additional response validations
   * @param {Object} response - PayU response
   * @returns {Object} Validation result
   */
  performAdditionalValidations(response) {
    const errors = [];

    // Check transaction ID format
    if (!response.txnid || !response.txnid.startsWith('DARVI_')) {
      errors.push('Invalid transaction ID format');
    }

    // Check amount
    if (!response.amount || parseFloat(response.amount) <= 0) {
      errors.push('Invalid amount in response');
    }

    // Check status
    const validStatuses = ['success', 'failure', 'pending', 'cancelled'];
    if (!response.status || !validStatuses.includes(response.status.toLowerCase())) {
      errors.push('Invalid payment status');
    }

    return {
      isValid: errors.length === 0,
      error: errors.length > 0 ? errors.join(', ') : null
    };
  }

  /**
   * Get default success URL
   * @returns {string} Success URL
   */
  getDefaultSuccessUrl() {
    return this.config.environment === 'production'
      ? 'https://darvigroup.in/payment-success'
      : 'http://localhost:3000/payment-success';
  }

  /**
   * Get default failure URL
   * @returns {string} Failure URL
   */
  getDefaultFailureUrl() {
    return this.config.environment === 'production'
      ? 'https://darvigroup.in/payment-failure'
      : 'http://localhost:3000/payment-failure';
  }

  /**
   * Log payment request details (without sensitive data)
   * @param {Object} paymentData - Payment data to log
   */
  logPaymentRequest(paymentData) {
    // Payment request logging removed for production
  }

  /**
   * Log response verification details
   * @param {Object} response - PayU response
   * @param {boolean} isHashValid - Hash validation result
   */
  logResponseVerification(response, isHashValid) {
    // Response verification logging removed for production
  }

  /**
   * Get payment configuration for frontend
   * @returns {Object} Configuration object
   */
  getPaymentConfig() {
    return {
      environment: this.config.environment,
      apiUrl: this.config.apiUrl,
      key: this.config.key,
      mid: this.config.mid,
      successUrl: this.getDefaultSuccessUrl(),
      failureUrl: this.getDefaultFailureUrl()
    };
  }
}

module.exports = PayUApiClient;
