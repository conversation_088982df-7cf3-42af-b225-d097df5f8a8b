import React from 'react';

interface DarviLogoProps {
  variant?: 'banner' | 'icon';
  className?: string;
}

const DarviLogo: React.FC<DarviLogoProps> = ({ variant = 'banner', className = '' }) => {
  const size = variant === 'banner' ? { width: '200px', height: '82px' } : { width: '32px', height: '32px' };

  return (
    <>
      {variant === 'banner' ? (
        <img
          src={process.env.PUBLIC_URL + "/darvi-logo.png"}
          alt="Darvi Logo"
          style={size}
          className={className}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.onerror = null; // Prevent infinite loop
            target.src = process.env.PUBLIC_URL + "/darvi-icon.png"; // Fallback to icon
          }}
        />
      ) : (
        <svg
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          style={size}
          className={className}
          viewBox="0 0 1080 442"
        >
          <path d="M0 0 C356.4 0 712.8 0 1080 0 C1080 145.86 1080 291.72 1080 442 C723.6 442 367.2 442 0 442 C0 296.14 0 150.28 0 0 Z" fill="#FEFEFE"/>
          <path d="M0 0 C13.695 -0.020625 27.39 -0.04125 41.5 -0.0625 C45.79193359 -0.071604 50.08386719 -0.08070801 54.50585938 -0.09008789 C59.82910156 -0.09460449 59.82910156 -0.09460449 62.33703613 -0.09544373 C64.05383566 -0.09695904 65.77063426 -0.10043279 67.48742676 -0.10557556 C98.06885193 -0.19244269 98.06885193 -0.19244269 112.0625 3.25 C112.99078613 3.46962402 113.91907227 3.68924805 114.87548828 3.91552734 C122.75436131 5.829727 130.38921117 8.20713802 138 11 C138.88816406 11.32355469 139.77632812 11.64710938 140.69140625 11.98046875 C142.46676498 12.63812338 144.23480383 13.31553618 146 14 C146 14.66 146 15.32 146 16 C147.051875 16.1546875 147.051875 16.1546875 148.125 16.3125 C151.41057396 17.09818073 154.02288928 18.41643047 157 20 C157.78375 20.391875 158.5675 20.78375 159.375 21.1875 C159.91125 21.455625 160.4475 21.72375 161 22 C161 22.66 161 23.32 161 24 C161.5775 24.0825 162.155 24.165 162.75 24.25 C165.64305558 25.21435186 166.86071763 26.83742236 169 29 C171.32664747 30.81830432 173.69484214 32.56789546 176.07421875 34.31640625 C178 36 178 36 179 39 C179.66 39 180.32 39 181 39 C181.0825 39.61875 181.165 40.2375 181.25 40.875 C181.77341441 43.15303678 181.77341441 43.15303678 183.8125 43.875 C187.0065746 45.51766694 188.2213211 47.95083618 190 51 C190 51.66 190 52.32 190 53 C190.66 53 191.32 53 192 53 C202.15301267 72.25268685 207.57250086 90.92387099 207.39111328 112.70898438 C207.3750049 114.99930312 207.39108699 117.28785839 207.41015625 119.578125 C207.42232528 129.68978045 206.21159237 139.13935431 204 149 C203.34 149 202.68 149 202 149 C201.88527344 149.84046875 201.77054688 150.6809375 201.65234375 151.546875 C200.94607861 155.28542821 199.84605389 158.66976067 198.5625 162.25 C198.13066406 163.47203125 197.69882812 164.6940625 197.25390625 165.953125 C196 169 196 169 194 171 C193.3574765 173.06874034 193.3574765 173.06874034 193 175 C192.34 175 191.68 175 191 175 C190.7525 175.680625 190.505 176.36125 190.25 177.0625 C188.75802092 180.56865083 186.93164489 183.71620368 185 187 C184.01 187 183.02 187 182 187 C181.73703125 187.58910156 181.4740625 188.17820312 181.203125 188.78515625 C178.08714028 194.52140085 172.12385014 199.06774291 167 203 C166.34 203 165.68 203 165 203 C165 203.66 165 204.32 165 205 C137.66652561 225.63151673 101.15736928 231.38288416 67.96875 231.1953125 C65.9962173 231.19157199 64.02368271 231.18872994 62.05114746 231.18673706 C56.91549344 231.17918218 51.77995658 231.15960592 46.64434814 231.1373291 C41.38140446 231.11669291 36.11844143 231.10769774 30.85546875 231.09765625 C20.57026165 231.0763505 10.28514065 231.04231581 0 231 C0 229.68 0 228.36 0 227 C0.71800781 227.0721875 1.43601563 227.144375 2.17578125 227.21875 C25.93673248 229.89538658 25.93673248 229.89538658 48 223 C49.299375 222.5978125 49.299375 222.5978125 50.625 222.1875 C55.05528153 220.62869724 58.95124136 218.35727725 63 216 C63.3373846 212.87501834 63.3373846 212.87501834 61 210.8125 C60.34 210.214375 59.68 209.61625 59 209 C59.66 209 60.32 209 61 209 C61 208.34 61 207.68 61 207 C62.58074564 206.18247697 64.16471223 205.37118003 65.75 204.5625 C67.07257813 203.88380859 67.07257813 203.88380859 68.421875 203.19140625 C70.64198566 202.16544602 72.61444619 201.48491397 75 201 C75 200.34 75 199.68 75 199 C75.66 199 76.32 199 77 199 C77.15149298 195.37114676 77.15149298 195.37114676 76 192 C80.625 187.125 80.625 187.125 84 186 C86.36506843 183.99878825 86.9893322 183.03200339 88 180 C86.02 179.01 86.02 179.01 84 178 C85.13719419 174.58841744 86.04342944 173.88145399 89 172 C89.66 172 90.32 172 91 172 C91 171.34 91 170.68 91 170 C92.32 169.34 93.64 168.68 95 168 C95 167.34 95 166.68 95 166 C94.34 165.67 93.68 165.34 93 165 C93.33 163.68 93.66 162.36 94 161 C94.66 161 95.32 161 96 161 C96.226875 160.484375 96.45375 159.96875 96.6875 159.4375 C98.18821778 156.65045269 99.98144856 154.21166052 101.87890625 151.68359375 C103.18551179 149.88113235 103.18551179 149.88113235 104 147 C102.68 147 101.36 147 100 147 C100.33 143.7 100.66 140.4 101 137 C101.66 137 102.32 137 103 137 C103.05534912 136.41508789 103.11069824 135.83017578 103.16772461 135.22753906 C103.42117265 132.56804892 103.67928825 129.90903168 103.9375 127.25 C104.02451172 126.32960938 104.11152344 125.40921875 104.20117188 124.4609375 C104.28818359 123.57148438 104.37519531 122.68203125 104.46484375 121.765625 C104.543396 120.94868164 104.62194824 120.13173828 104.70288086 119.29003906 C105 117 105 117 105.50949097 115.16772461 C106.06171642 112.72725437 106.13425833 110.52849069 106.14526367 108.02636719 C106.15164352 107.03723328 106.15802338 106.04809937 106.16459656 105.0289917 C106.16570938 103.96239319 106.1668222 102.89579468 106.16796875 101.796875 C106.17129715 100.68628723 106.17462555 99.57569946 106.17805481 98.43145752 C106.1831203 96.07442641 106.18546325 93.71738811 106.18530273 91.36035156 C106.1874664 87.80521255 106.20556252 84.25039566 106.22460938 80.6953125 C106.25109245 69.65701042 106.10908189 58.87339327 104.4309082 47.95019531 C104 45 104 45 104 41 C103.34 41 102.68 41 102 41 C101.92265625 41.82177734 101.92265625 41.82177734 101.84375 42.66015625 C100.98390435 48.66930484 98.96948037 53.51810041 96.125 58.8125 C95.78533203 59.44615479 95.44566406 60.07980957 95.09570312 60.73266602 C93.03823068 64.44346453 90.86035322 67.85839866 88 71 C87.34 71 86.68 71 86 71 C85.62875 72.0828125 85.62875 72.0828125 85.25 73.1875 C82.76868751 78.77045311 78.77255981 82.3687045 74 86 C73.34 86 72.68 86 72 86 C72 86.66 72 87.32 72 88 C68.78170929 90.96082745 65.00499517 92.35509127 61 94 C58.69769234 94.89831016 58.69769234 94.89831016 57 96 C56.505 97.485 56.505 97.485 56 99 C54.1875 100.09765625 54.1875 100.09765625 52 101.0625 C48.8485304 102.32230077 48.8485304 102.32230077 47 105 C45.68 105 44.36 105 43 105 C42.505 106.485 42.505 106.485 42 108 C41.01 108.495 41.01 108.495 40 109 C39.505 110.485 39.505 110.485 39 112 C36.03 112.495 36.03 112.495 33 113 C33 111.35 33 109.7 33 108 C31.05000871 108.19471111 31.05000871 108.19471111 29 109 C28.31513275 110.32407669 27.65129797 111.65909241 27 113 C26.21625 113.5775 25.4325 114.155 24.625 114.75 C21.59543361 117.34677119 20.51069921 119.67138551 18.9921875 123.29296875 C18 125 18 125 15 126 C14.875 120.25 14.875 120.25 16 118 C13.525 118.495 13.525 118.495 11 119 C11 119.66 11 120.32 11 121 C9.81897756 122.14727894 8.63590442 123.2925394 7.44140625 124.42578125 C5.22717351 126.84403543 3.66403556 129.60366064 2.015625 132.42578125 C1.68046875 132.94527344 1.3453125 133.46476563 1 134 C0.67 134 0.34 134 0 134 C0 89.78 0 45.56 0 0 Z" fill="#A9CE46"/>
        </svg>
      )}
    </>
  );
};

export default DarviLogo;