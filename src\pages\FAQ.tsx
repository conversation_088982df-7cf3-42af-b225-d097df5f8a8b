import React, { useState } from 'react';
import {
  Container,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const FAQ = () => {
  const [expanded, setExpanded] = useState<string | false>(false);

  const handleChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  const faqs = [
    {
      question: 'What services does Darvi Group provide?',
      answer: 'Darvi Group is a B2B and B2C provider of premium agricultural solutions. Our core services include: 1) Agricultural consultation services for both small farmers and commercial enterprises, 2) IoT-based smart farming solutions for optimizing crop yields and resource management, and 3) Sustainable farming practices training and implementation.',
    },
    {
      question: 'What types of agricultural solutions do you offer?',
      answer: 'We specialize in sustainable farming practices, soil health management, crop optimization, and valuable timber species like sandalwood. All our solutions are carefully designed to ensure quality and effectiveness for various agricultural needs.',
    },
    {
      question: 'Do you offer services for commercial farms?',
      answer: 'Yes, we specialize in providing comprehensive agricultural solutions for commercial farms and plantations. We offer competitive pricing for large-scale projects and can customize our services to meet your specific needs. We\'ve successfully implemented sustainable farming solutions for numerous commercial projects throughout Karnataka and neighboring states.',
    },
    {
      question: 'How can I register for Darvi Group services?',
      answer: 'You can register for our services by filling out the registration form on our website. The form requires basic information about you, your land, and the specific services you are interested in. After submission, our team will review your application and contact you for further details.',
    },
    {
      question: 'What documents do I need to provide?',
      answer: 'For our services, you\'ll need to provide your Aadhaar number, land documents (RTC/Survey number), and proof of address. Additional documents may be required based on the specific services you are interested in.',
    },
    {
      question: 'How long does it take to process my registration?',
      answer: 'Typically, we process registrations within 2-3 business days. Our team will verify your documents and contact you to schedule a site visit if required. Processing times may vary depending on the complexity of your requirements and your location.',
    },
    {
      question: 'Do you provide services in my area?',
      answer: 'Darvi Group currently operates primarily in Karnataka, with a focus on sustainable agricultural development. We provide services to most locations in Karnataka and neighboring states. For consultation and IoT services, please contact us to confirm availability in your specific location.',
    },
    {
      question: 'What are your pricing structures?',
      answer: 'Our pricing varies based on the type of service and the size of your land. Consultation fees vary based on the complexity of your requirements. We offer both one-time consultations and ongoing support packages. IoT solutions are priced based on the scale of implementation and specific requirements. Contact us for detailed pricing information.',
    },
    {
      question: 'Do you provide guidance after implementing your solutions?',
      answer: 'Yes, we provide detailed instructions and follow-up support with all our services. For larger projects, we can arrange training sessions for your staff on proper implementation and maintenance techniques. We also offer ongoing consultation services to ensure the effectiveness and sustainability of our solutions.',
    },
    {
      question: 'How can I contact Darvi Group?',
      answer: 'You can contact us through our website\'s contact form, by phone at +91 99868 90777, or by visiting our office at #2 Totad building, Arjun Vihar Gokul road Hubli, Karnataka 580030, India. Our business hours are Monday through Saturday, 9:00 AM to 6:00 PM.',
    },
  ];

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom align="center" color="primary">
        Frequently Asked Questions
      </Typography>
      <Typography variant="subtitle1" align="center" paragraph color="text.secondary">
        Find answers to common questions about our services and registration process
      </Typography>

      <Box sx={{ mt: 4 }}>
        {faqs.map((faq, index) => (
          <Accordion
            key={index}
            expanded={expanded === `panel${index}`}
            onChange={handleChange(`panel${index}`)}
            sx={{ mb: 1 }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls={`panel${index}bh-content`}
              id={`panel${index}bh-header`}
            >
              <Typography variant="h6">{faq.question}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography color="text.secondary">
                {faq.answer}
              </Typography>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    </Container>
  );
};

export default FAQ;