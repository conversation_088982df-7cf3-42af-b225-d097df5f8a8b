/**
 * PayU Payment Failure Handler
 * Converted from PHP failure.php to JavaScript Netlify Function
 * Handles failed payment responses from PayU
 */

// Import utilities
const { getPayUConfig } = require('./utils/payuConfig');
const { verifyPayUHash } = require('./utils/hashGenerator');
const { validatePayUResponse, sanitizePayUResponse } = require('./utils/payuValidator');
const { logPaymentResponse, logError, debugLog } = require('./utils/logger');

// CORS headers
const getCORSHeaders = (origin) => {
  const allowedOrigins = process.env.NODE_ENV === 'production' 
    ? ['https://darvigroup.in', 'https://www.darvigroup.in']
    : ['http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000'];
  
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  return {
    'Access-Control-Allow-Origin': isAllowedOrigin ? origin : allowedOrigins[0],
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Content-Type': 'application/json',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block'
  };
};

// Main handler
exports.handler = async (event) => {
  debugLog('PayU failure handler called', {
    method: event.httpMethod,
    hasBody: !!event.body,
    hasQueryParams: !!event.queryStringParameters
  });

  const origin = event.headers.origin || event.headers.Origin;
  const corsHeaders = getCORSHeaders(origin);

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    const config = getPayUConfig();
    let responseData = {};

    // Parse PayU response data (can come via POST or GET)
    if (event.httpMethod === 'POST' && event.body) {
      try {
        // Try to parse as JSON first
        responseData = JSON.parse(event.body);
      } catch (jsonError) {
        // If not JSON, try to parse as form data
        const formData = new URLSearchParams(event.body);
        responseData = Object.fromEntries(formData.entries());
      }
    } else if (event.queryStringParameters) {
      // Handle GET request with query parameters
      responseData = event.queryStringParameters;
    }

    debugLog('PayU failure response data received', {
      txnid: responseData.txnid,
      status: responseData.status,
      amount: responseData.amount,
      error: responseData.error,
      error_Message: responseData.error_Message
    });

    // Sanitize response data
    const sanitized = sanitizePayUResponse(responseData);

    // Extract key parameters
    const {
      mihpayid = '',
      mode = '',
      status = '',
      key = '',
      txnid = '',
      amount = '',
      productinfo = '',
      firstname = '',
      lastname = '',
      email = '',
      phone = '',
      hash = '',
      error = '',
      error_Message = '',
      bank_ref_num = '',
      bankcode = '',
      cardnum = '',
      name_on_card = '',
      udf1 = '',
      udf2 = '',
      udf3 = '',
      udf4 = '',
      udf5 = ''
    } = sanitized;

    // Verify hash for security (even for failed transactions)
    let hashVerified = false;
    let hashVerification = { isValid: false, message: 'Hash verification skipped' };

    if (hash && key && txnid) {
      hashVerification = verifyPayUHash(
        {
          key,
          txnid,
          amount,
          productinfo,
          firstname,
          email,
          status,
          udf1,
          udf2,
          udf3,
          udf4,
          udf5
        },
        hash,
        config.salt
      );
      hashVerified = hashVerification.isValid;
    }

    // Log the failed transaction response
    logPaymentResponse(sanitized, hashVerified);

    // Determine failure reason
    let failureReason = 'Payment failed';
    if (error_Message) {
      failureReason = error_Message;
    } else if (error) {
      failureReason = error;
    } else if (status) {
      switch (status.toLowerCase()) {
        case 'failure':
          failureReason = 'Payment was declined';
          break;
        case 'cancel':
          failureReason = 'Payment was cancelled by user';
          break;
        case 'pending':
          failureReason = 'Payment is pending';
          break;
        default:
          failureReason = `Payment status: ${status}`;
      }
    }

    // Prepare response data
    const transactionDetails = {
      txnid,
      mihpayid,
      status,
      amount,
      mode,
      firstname,
      lastname,
      email,
      phone,
      productinfo,
      bank_ref_num,
      bankcode,
      error,
      error_message: error_Message,
      failure_reason: failureReason,
      hash_verified: hashVerified,
      timestamp: new Date().toISOString()
    };

    debugLog('Payment failure processed', {
      txnid,
      status,
      failure_reason: failureReason,
      hash_verified: hashVerified
    });

    // Return failure response
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: 'Payment failed',
        data: {
          transaction: transactionDetails,
          failure_reason: failureReason,
          security: {
            hash_verified: hashVerified,
            hash_message: hashVerification.message
          }
        }
      })
    };

  } catch (error) {
    logError('PayU failure handler error', {
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });

    const errorMessage = process.env.NODE_ENV === 'production'
      ? 'Payment processing failed. Please contact support.'
      : error.message || 'Payment processing failed';

    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: errorMessage,
        timestamp: new Date().toISOString()
      })
    };
  }
};
