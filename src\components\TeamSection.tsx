import React from 'react';
import { Box, Typography, Grid, Card, CardContent, Avatar, Chip, useTheme } from '@mui/material';
import { useLanguage } from '../contexts/LanguageContext';
import AnimatedText from './AnimatedText';
import { motion } from 'framer-motion';
import teamContent from '../content/home/<USER>';
import PersonIcon from '@mui/icons-material/Person';
import WorkIcon from '@mui/icons-material/Work';
import SchoolIcon from '@mui/icons-material/School';

const TeamSection: React.FC = () => {
  const { language } = useLanguage();
  const theme = useTheme();

  return (
    <Box
      sx={{
        py: { xs: 6, md: 8 },
        px: { xs: 2, md: 4 },
        position: 'relative',
        background: `linear-gradient(135deg,
          rgba(27, 76, 53, 0.95) 0%,
          rgba(46, 125, 50, 0.9) 25%,
          rgba(76, 175, 80, 0.85) 50%,
          rgba(139, 195, 74, 0.8) 75%,
          rgba(27, 76, 53, 0.95) 100%
        )`,
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 40% 60%, rgba(76, 175, 80, 0.1) 0%, transparent 50%)
          `,
          pointerEvents: 'none'
        },
        '&::after': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          pointerEvents: 'none'
        }
      }}
    >
      <Box sx={{ position: 'relative', zIndex: 1 }}>
        <Box sx={{ textAlign: 'center', mb: { xs: 4, md: 6 } }}>
          <AnimatedText
            text={teamContent.sectionTitle[language as keyof typeof teamContent.sectionTitle] || teamContent.sectionTitle.en}
            animation="fadeIn"
            delay={0.2}
            duration={0.8}
            variant="h3"
            sx={{
              mb: 2,
              color: 'white',
              fontSize: { xs: '2rem', md: '2.5rem', lg: '3rem' },
              fontWeight: 700,
              textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
            }}
          />
          <Typography
            variant="subtitle1"
            sx={{
              maxWidth: '800px',
              mx: 'auto',
              mb: 4,
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: { xs: '1rem', md: '1.1rem' },
              textShadow: '1px 1px 2px rgba(0,0,0,0.2)'
            }}
          >
            {teamContent.sectionSubtitle[language as keyof typeof teamContent.sectionSubtitle] || teamContent.sectionSubtitle.en}
          </Typography>
        </Box>

        <Grid container spacing={{ xs: 1.5, sm: 2, md: 4 }} justifyContent="center" sx={{ maxWidth: '1400px', mx: 'auto' }}>
          {teamContent.teamMembers.map((member, index) => {
            // Mobile 2x2+1 layout: First 4 members in 2x2 grid, 5th member centered
            // Desktop: Standard 4-column layout
            const isLastMember = index === 4;

            return (
              <Grid
                item
                xs={isLastMember ? 12 : 6}  // Mobile: 2x2+1 (2 cols, then 1 centered)
                sm={isLastMember ? 12 : 6}  // Small: 2x2+1 (2 cols, then 1 centered)
                md={4}                      // Medium+: Standard 4-column layout
                lg={3}                      // Large: Standard 4-column layout
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: {
                    xs: isLastMember ? 'center' : 'stretch',
                    sm: isLastMember ? 'center' : 'stretch',
                    md: 'stretch'
                  },
                  ...(isLastMember && {
                    '& > div': {
                      maxWidth: {
                        xs: '300px',  // Smaller on mobile
                        sm: '350px',  // Medium on small screens
                        md: '100%'    // Full width on desktop
                      },
                      width: '100%'
                    }
                  })
                }}
              >
              <motion.div
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 100
                }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
              >
                <Card
                  sx={{
                    height: '100%',
                    minHeight: { xs: '280px', md: '350px' }, // Shorter on mobile
                    display: 'flex',
                    flexDirection: 'column',
                    background: 'rgba(255, 255, 255, 0.95)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: { xs: '16px', md: '20px' }, // Smaller radius on mobile
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    overflow: 'hidden',
                    position: 'relative',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '4px',
                      background: `linear-gradient(90deg,
                        ${theme.palette.primary.main} 0%,
                        ${theme.palette.secondary.main} 50%,
                        ${theme.palette.primary.main} 100%
                      )`,
                    },
                    '&:hover': {
                      transform: { xs: 'translateY(-4px)', md: 'translateY(-12px) scale(1.02)' }, // Less movement on mobile
                      boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
                      background: 'rgba(255, 255, 255, 0.98)',
                    }
                  }}
                >
                  {/* Profile Image Section */}
                  <Box sx={{ p: { xs: 1.5, md: 3 }, textAlign: 'center' }}>
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ duration: 0.3, type: "spring" }}
                    >
                      <Avatar
                        src={member.image}
                        alt={member.name}
                        sx={{
                          width: { xs: 70, sm: 80, md: 100 },
                          height: { xs: 70, sm: 80, md: 100 },
                          mx: 'auto',
                          mb: { xs: 1, md: 2 },
                          border: '3px solid',
                          borderColor: theme.palette.primary.main,
                          boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                          background: `linear-gradient(135deg, ${theme.palette.primary.light}, ${theme.palette.primary.main})`,
                          '& .MuiAvatar-img': {
                            transition: 'transform 0.3s ease',
                          },
                          '&:hover .MuiAvatar-img': {
                            transform: 'scale(1.1)',
                          }
                        }}
                      >
                        <PersonIcon sx={{ fontSize: { xs: 35, md: 50 }, color: 'white' }} />
                      </Avatar>
                    </motion.div>

                    {/* Position Chip */}
                    <Chip
                      label={member.position}
                      size="small"
                      sx={{
                        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                        color: 'white',
                        fontWeight: 600,
                        fontSize: { xs: '0.7rem', md: '0.75rem' },
                        mb: { xs: 0.5, md: 1 },
                        '& .MuiChip-label': {
                          px: { xs: 1, md: 1.5 }
                        }
                      }}
                    />
                  </Box>

                  {/* Card Content */}
                  <CardContent sx={{ flexGrow: 1, p: { xs: 1.5, md: 3 }, pt: 0 }}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                    >
                      <Typography
                        variant="h6"
                        component="h3"
                        gutterBottom
                        sx={{
                          fontSize: { xs: '0.95rem', md: '1.1rem' },
                          fontWeight: 700,
                          color: theme.palette.primary.main,
                          textAlign: 'center',
                          mb: { xs: 0.5, md: 1 }
                        }}
                      >
                        {member.name}
                      </Typography>
                    </motion.div>

                    {/* Education Info - Hidden on mobile */}
                    <Box sx={{ display: { xs: 'none', md: 'block' } }}>
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: 0.4 }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5, justifyContent: 'center' }}>
                          <SchoolIcon sx={{ fontSize: 16, color: theme.palette.secondary.main, mr: 1 }} />
                          <Typography
                            variant="caption"
                            sx={{
                              fontSize: '0.7rem',
                              color: 'text.secondary',
                              textAlign: 'center',
                              lineHeight: 1.2
                            }}
                          >
                            {member.education}
                          </Typography>
                        </Box>
                      </motion.div>
                    </Box>

                    {/* Specialization - Hidden on mobile */}
                    <Box sx={{ display: { xs: 'none', md: 'block' } }}>
                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: 0.5 }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, justifyContent: 'center' }}>
                          <WorkIcon sx={{ fontSize: 16, color: theme.palette.primary.main, mr: 1 }} />
                          <Typography
                            variant="caption"
                            sx={{
                              fontSize: '0.7rem',
                              color: 'text.secondary',
                              textAlign: 'center',
                              lineHeight: 1.2
                            }}
                          >
                            {member.specialization}
                          </Typography>
                        </Box>
                      </motion.div>
                    </Box>

                    {/* Description */}
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.6 }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontSize: { xs: '0.75rem', md: '0.8rem' },
                          color: 'text.secondary',
                          textAlign: 'center',
                          lineHeight: 1.4,
                          display: '-webkit-box',
                          WebkitLineClamp: { xs: 2, md: 3 }, // Fewer lines on mobile
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}
                      >
                        {member.shortDescription}
                      </Typography>
                    </motion.div>
                  </CardContent>
                </Card>
              </motion.div>
              </Grid>
            );
          })}
        </Grid>

        {/* View All Team Link */}
        <Box sx={{ textAlign: 'center', mt: { xs: 4, md: 6 } }}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            viewport={{ once: true }}
            whileHover={{ scale: 1.05 }}
          >
            <Box
              component="a"
              href="/about#team"
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                px: 4,
                py: 2,
                background: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(10px)',
                borderRadius: '50px',
                textDecoration: 'none',
                color: theme.palette.primary.main,
                fontWeight: 600,
                fontSize: { xs: '0.9rem', md: '1rem' },
                border: `2px solid ${theme.palette.primary.main}`,
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                '&:hover': {
                  background: theme.palette.primary.main,
                  color: 'white',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 30px rgba(0, 0, 0, 0.2)',
                }
              }}
            >
              {teamContent.viewAllLinkText[language as keyof typeof teamContent.viewAllLinkText] || teamContent.viewAllLinkText.en}
            </Box>
          </motion.div>
        </Box>
      </Box>
    </Box>
  );
};

export default TeamSection;
