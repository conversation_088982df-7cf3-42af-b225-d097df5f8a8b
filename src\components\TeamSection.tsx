import React from 'react';
import { Box, Typography, Card, Avatar, Chip, useTheme, Container } from '@mui/material';
import { useLanguage } from '../contexts/LanguageContext';
import AnimatedText from './AnimatedText';
import { motion } from 'framer-motion';
import teamContent from '../content/home/<USER>';
import PersonIcon from '@mui/icons-material/Person';

import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

const TeamSection: React.FC = () => {
  const { language } = useLanguage();
  const theme = useTheme();

  return (
    <Box
      sx={{
        py: { xs: 3, md: 4 },
        background: `linear-gradient(135deg,
          ${theme.palette.primary.main} 0%,
          ${theme.palette.primary.dark} 100%
        )`,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: { xs: 2, md: 3 } }}>
          <AnimatedText
            text={teamContent.sectionTitle[language as keyof typeof teamContent.sectionTitle] || teamContent.sectionTitle.en}
            animation="fadeIn"
            delay={0.2}
            duration={0.8}
            variant="h4"
            sx={{
              mb: 1,
              color: 'white',
              fontSize: { xs: '1.5rem', md: '2rem' },
              fontWeight: 600,
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
            }}
          />
          <Typography
            variant="subtitle2"
            sx={{
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: { xs: '0.9rem', md: '1rem' },
              maxWidth: '600px',
              mx: 'auto'
            }}
          >
            {teamContent.sectionSubtitle[language as keyof typeof teamContent.sectionSubtitle] || teamContent.sectionSubtitle.en}
          </Typography>
        </Box>

        {/* Team Cards - Horizontal Scroll on Mobile, Grid on Desktop */}
        <Box
          sx={{
            display: 'flex',
            gap: { xs: 2, md: 3 },
            overflowX: { xs: 'auto', md: 'visible' },
            overflowY: 'visible',
            pb: { xs: 2, md: 0 },
            px: { xs: 1, md: 0 },
            justifyContent: { xs: 'flex-start', md: 'center' },
            flexWrap: { xs: 'nowrap', md: 'wrap' },
            '&::-webkit-scrollbar': {
              height: 6,
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: 3,
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(255, 255, 255, 0.3)',
              borderRadius: 3,
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
              },
            },
          }}
        >
          {teamContent.teamMembers.map((member, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.5,
                delay: index * 0.1,
              }}
              viewport={{ once: true }}
              whileHover={{ y: -5, scale: 1.02 }}
              style={{
                minWidth: '240px',
                maxWidth: '240px',
                flex: '0 0 auto'
              }}
            >
              <Card
                sx={{
                  height: '200px',
                  display: 'flex',
                  flexDirection: 'column',
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(10px)',
                  borderRadius: '16px',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease',
                  overflow: 'hidden',
                  position: 'relative',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '3px',
                    background: `linear-gradient(90deg,
                      ${theme.palette.primary.main} 0%,
                      ${theme.palette.secondary.main} 100%
                    )`,
                  },
                  '&:hover': {
                    boxShadow: '0 8px 30px rgba(0, 0, 0, 0.15)',
                    background: 'rgba(255, 255, 255, 0.98)',
                  }
                }}
              >
                {/* Profile Section */}
                <Box sx={{ p: 2, textAlign: 'center', flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <Avatar
                    src={member.image}
                    alt={member.name}
                    sx={{
                      width: 60,
                      height: 60,
                      mx: 'auto',
                      mb: 1,
                      border: '2px solid',
                      borderColor: theme.palette.primary.main,
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                    }}
                  >
                    <PersonIcon sx={{ fontSize: 30, color: 'white' }} />
                  </Avatar>

                  <Typography
                    variant="h6"
                    sx={{
                      fontSize: '1rem',
                      fontWeight: 600,
                      color: theme.palette.primary.main,
                      mb: 0.5,
                      lineHeight: 1.2
                    }}
                  >
                    {member.name}
                  </Typography>

                  <Chip
                    label={member.position}
                    size="small"
                    sx={{
                      background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                      color: 'white',
                      fontWeight: 500,
                      fontSize: '0.7rem',
                      mb: 1,
                      height: '20px'
                    }}
                  />

                  <Typography
                    variant="body2"
                    sx={{
                      fontSize: '0.75rem',
                      color: 'text.secondary',
                      textAlign: 'center',
                      lineHeight: 1.3,
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden'
                    }}
                  >
                    {member.shortDescription}
                  </Typography>
                </Box>
              </Card>
            </motion.div>
          ))}
        </Box>

        {/* View All Team Link */}
        <Box sx={{ textAlign: 'center', mt: 3 }}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            viewport={{ once: true }}
            whileHover={{ scale: 1.05 }}
          >
            <Box
              component="a"
              href="/about#team"
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: 1,
                px: 3,
                py: 1.5,
                background: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(10px)',
                borderRadius: '25px',
                textDecoration: 'none',
                color: theme.palette.primary.main,
                fontWeight: 600,
                fontSize: '0.9rem',
                border: `2px solid ${theme.palette.primary.main}`,
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
                '&:hover': {
                  background: theme.palette.primary.main,
                  color: 'white',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 6px 20px rgba(0, 0, 0, 0.15)',
                }
              }}
            >
              {teamContent.viewAllLinkText[language as keyof typeof teamContent.viewAllLinkText] || teamContent.viewAllLinkText.en}
              <ArrowForwardIcon sx={{ fontSize: 16 }} />
            </Box>
          </motion.div>
        </Box>
      </Container>
    </Box>
  );
};

export default TeamSection;
