import config from '../config';

interface EmailData {
  name: string;
  email: string;
  subject: string;
  message: string;
  type?: string;
}

class EmailService {
  private webhookUrl: string;
  private contactEmail: string;

  constructor() {
    // New webhook URL for n8n
    this.webhookUrl = 'https://aimania.app.n8n.cloud/webhook/https://darvigroup.in/contact';
    this.contactEmail = config.contactEmail;
  }

  /**
   * Sends an email using n8n webhook
   * @param data Email data including name, email, subject, and message
   * @returns Promise with success status and message
   */
  async sendEmail(data: EmailData): Promise<{ success: boolean; message: string }> {
    try {
      // Add recipient email
      const emailData = {
        ...data,
        type: 'contact',
        to: this.contactEmail, // Set the recipient email from config
      };



      // Send the data to n8n webhook
      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      return {
        success: true,
        message: 'Email sent successfully',
      };
    } catch (error) {
      // Error sending email
      return {
        success: false,
        message: 'Failed to send email. Please try again later.',
      };
    }
  }
}

const emailService = new EmailService();
export default emailService;
