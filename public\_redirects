# Netlify redirects for SPA routing and PayU integration
# PayU callback handlers MUST be first to intercept payment responses

# PayU Payment Callback Handlers (HIGHEST PRIORITY)
/payu/success    /.netlify/functions/payu-callback-handler    200
/payu/failure    /.netlify/functions/payu-callback-handler    200
/payu/cancel     /.netlify/functions/payu-callback-handler    200
/payu/webhook    /.netlify/functions/payu-webhook-handler     200

# Handle form route specifically
/form    /index.html   200

# Handle payment pages specifically
/payment-success    /index.html   200
/payment-failure    /index.html   200

# Handle all other routes (SPA fallback - MUST BE LAST)
/*    /index.html   200
