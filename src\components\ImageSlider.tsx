import React, { useState, useEffect, useCallback } from 'react';
import { Box, IconButton, Typography, useTheme, useMediaQuery } from '@mui/material';
import { styled } from '@mui/material/styles';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';

interface SlideItem {
  image: string;
  title?: string;
  description?: string;
  url?: string;
}

interface ImageSliderProps {
  slides: SlideItem[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  height?: string | number;
  showArrows?: boolean;
  showDots?: boolean;
  showCaption?: boolean;
  borderRadius?: string | number;
  effect?: 'fade' | 'slide';
}

const SliderContainer = styled(Box)<{ borderRadius: string | number }>(({ borderRadius }) => ({
  position: 'relative',
  overflow: 'hidden',
  width: '100%',
  borderRadius,
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)',
}));

const SlideImage = styled('img')({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  display: 'block',
});

const SlideCaption = styled(Box)(({ theme }) => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  padding: theme.spacing(3),
  background: 'linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0))',
  color: 'white',
  textAlign: 'left',
}));

const NavigationArrow = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  zIndex: 2,
  color: 'white',
  backgroundColor: 'rgba(0, 0, 0, 0.3)',
  '&:hover': {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(0.5),
  },
}));

const DotsContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  bottom: theme.spacing(1),
  left: '50%',
  transform: 'translateX(-50%)',
  display: 'flex',
  gap: theme.spacing(1),
  zIndex: 2,
}));

const DotIndicator = styled(FiberManualRecordIcon)<{ active: boolean }>(({ active, theme }) => ({
  fontSize: active ? 12 : 10,
  color: active ? theme.palette.primary.main : 'rgba(255, 255, 255, 0.7)',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    color: active ? theme.palette.primary.main : 'rgba(255, 255, 255, 0.9)',
  },
}));

const ImageSlider: React.FC<ImageSliderProps> = ({
  slides,
  autoPlay = true,
  autoPlayInterval = 5000,
  height = 500,
  showArrows = true,
  showDots = true,
  showCaption = true,
  borderRadius = '16px',
  effect = 'fade',
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
  }, [slides.length]);

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
  }, [slides.length]);

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index);
  }, []);

  const handleSlideClick = (url?: string) => {
    if (url) {
      window.open(url, '_blank');
    }
  };

  useEffect(() => {
    if (autoPlay && !isHovered) {
      const interval = setInterval(nextSlide, autoPlayInterval);
      return () => clearInterval(interval);
    }
  }, [autoPlay, autoPlayInterval, isHovered, nextSlide]);

  return (
    <SliderContainer
      borderRadius={borderRadius}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      sx={{ height }}
    >
      {slides.map((slide, index) => (
        <Box
          key={index}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            opacity: currentSlide === index ? 1 : 0,
            transition: effect === 'fade'
              ? 'opacity 0.8s ease-in-out'
              : 'transform 0.8s ease-in-out, opacity 0.8s ease-in-out',
            transform: effect === 'slide'
              ? `translateX(${(index - currentSlide) * 100}%)`
              : 'none',
            cursor: slide.url ? 'pointer' : 'default',
          }}
          onClick={() => handleSlideClick(slide.url)}
        >
          <SlideImage src={slide.image} alt={slide.title || `Slide ${index + 1}`} />

          {showCaption && (slide.title || slide.description) && (
            <SlideCaption>
              {slide.title && (
                <Typography
                  variant={isMobile ? 'h6' : 'h5'}
                  component="h3"
                  gutterBottom
                  sx={{
                    fontWeight: 600,
                    textShadow: '0 2px 4px rgba(0,0,0,0.5)'
                  }}
                >
                  {slide.title}
                </Typography>
              )}
              {slide.description && (
                <Typography
                  variant="body2"
                  sx={{
                    maxWidth: '80%',
                    display: { xs: 'none', sm: 'block' },
                    textShadow: '0 1px 2px rgba(0,0,0,0.5)'
                  }}
                >
                  {slide.description}
                </Typography>
              )}
            </SlideCaption>
          )}
        </Box>
      ))}

      {showArrows && (
        <>
          <NavigationArrow
            onClick={prevSlide}
            sx={{ left: { xs: 8, md: 16 } }}
            size={isMobile ? 'small' : 'medium'}
          >
            <ArrowBackIosNewIcon fontSize={isMobile ? 'small' : 'medium'} />
          </NavigationArrow>
          <NavigationArrow
            onClick={nextSlide}
            sx={{ right: { xs: 8, md: 16 } }}
            size={isMobile ? 'small' : 'medium'}
          >
            <ArrowForwardIosIcon fontSize={isMobile ? 'small' : 'medium'} />
          </NavigationArrow>
        </>
      )}

      {showDots && (
        <DotsContainer>
          {slides.map((_, index) => (
            <DotIndicator
              key={index}
              active={currentSlide === index}
              onClick={() => goToSlide(index)}
            />
          ))}
        </DotsContainer>
      )}
    </SliderContainer>
  );
};

export default ImageSlider;
