<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Darvi Group Content Manager</title>

    <!-- Content Security Policy for Netlify CMS Admin -->
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://identity.netlify.com https://unpkg.com https://cdn.jsdelivr.net https://*.netlify.com https://*.netlify.app;
      style-src 'self' 'unsafe-inline' https://unpkg.com https://cdn.jsdelivr.net https://fonts.googleapis.com;
      font-src 'self' https://fonts.gstatic.com https://unpkg.com https://cdn.jsdelivr.net;
      img-src 'self' data: https: blob:;
      connect-src 'self' https://api.netlify.com https://*.netlify.com https://*.netlify.app https://api.github.com https://uploads.github.com;
      frame-src 'self' https://*.netlify.com https://*.netlify.app;
      object-src 'none';
      base-uri 'self';
    " />

    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
  </head>
  <body>
    <!-- Include the script that builds the page and powers Netlify CMS -->
    <script src="https://unpkg.com/netlify-cms@2.10.192/dist/netlify-cms.js"></script>

    <script>
      // Initialize Netlify Identity
      if (window.netlifyIdentity) {
        window.netlifyIdentity.on("init", user => {
          if (!user) {
            window.netlifyIdentity.on("login", () => {
              document.location.href = "/admin/";
            });
          }
        });
      }
    </script>
  </body>
</html>
