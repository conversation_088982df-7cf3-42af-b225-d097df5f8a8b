/**
 * PayU Payment Initiation Function
 * Converted from PHP process_payment.php to JavaScript Netlify Function
 * Handles payment request and hash generation with proper debugging
 */

// Import utilities
const { validateEnvironment } = require('./utils/payuConfig');
const { generatePaymentHash, validateHashParams } = require('./utils/hashGenerator');
const { validatePaymentRequest, sanitizePaymentParams, generateTransactionId } = require('./utils/payuValidator');
const { logPaymentInitiation, logError, debugLog } = require('./utils/logger');

// CORS headers for security
const getCORSHeaders = (origin) => {
  const allowedOrigins = process.env.NODE_ENV === 'production' 
    ? ['https://darvigroup.in', 'https://www.darvigroup.in']
    : ['http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000'];
  
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  return {
    'Access-Control-Allow-Origin': isAllowedOrigin ? origin : allowedOrigins[0],
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  };
};

// Main handler
exports.handler = async (event) => {
  debugLog('PayU initiate function called', {
    method: event.httpMethod,
    headers: Object.keys(event.headers || {}),
    bodyLength: event.body?.length || 0
  });

  const origin = event.headers.origin || event.headers.Origin;
  const corsHeaders = getCORSHeaders(origin);

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: 'Method not allowed'
      })
    };
  }

  try {
    // Validate environment first
    const envValidation = validateEnvironment();
    if (!envValidation.isValid) {
      logError('Environment validation failed', { errors: envValidation.errors });
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Server configuration error',
          errors: process.env.NODE_ENV === 'production' ? undefined : envValidation.errors
        })
      };
    }

    const config = envValidation.config;

    // Log configuration status (without sensitive data)
    debugLog('PayU configuration loaded', {
      environment: process.env.NODE_ENV,
      testMode: config.testMode,
      merchantId: config.merchantId,
      paymentUrl: config.paymentUrl,
      hasCredentials: !!(config.merchantKey && config.salt)
    });

    // Parse request body
    let requestData;
    try {
      requestData = JSON.parse(event.body);
    } catch (parseError) {
      logError('JSON parse error', { error: parseError.message });
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Invalid JSON in request body'
        })
      };
    }

    debugLog('Payment initiation request received', {
      txnid: requestData.txnid,
      amount: requestData.amount,
      email: requestData.email?.substring(0, 5) + '***'
    });

    // Validate payment request
    const validation = validatePaymentRequest(requestData);
    if (!validation.isValid) {
      logError('Payment validation failed', { errors: validation.errors });
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Validation failed',
          errors: validation.errors
        })
      };
    }

    // Sanitize input data
    const sanitized = sanitizePaymentParams(requestData);

    // Generate transaction ID if not provided
    if (!sanitized.txnid) {
      sanitized.txnid = generateTransactionId();
    }

    // Prepare PayU payment data
    const paymentData = {
      key: config.merchantKey,
      txnid: sanitized.txnid,
      amount: sanitized.amount,
      productinfo: sanitized.productinfo,
      firstname: sanitized.firstname,
      email: sanitized.email,
      phone: sanitized.phone,
      surl: config.successUrl,
      furl: config.failureUrl,
      curl: config.cancelUrl || config.failureUrl, // Cancel URL for user cancellations
      
      // Optional fields
      lastname: sanitized.lastname,
      address1: sanitized.address1,
      city: sanitized.city || '',
      state: sanitized.state || 'Karnataka',
      country: sanitized.country || 'India',
      zipcode: sanitized.zipcode || '',
      
      // User defined fields
      udf1: sanitized.udf1 || '',
      udf2: sanitized.udf2 || '',
      udf3: sanitized.udf3 || '',
      udf4: sanitized.udf4 || '',
      udf5: sanitized.udf5 || '',
      
      // PayU specific parameters
      service_provider: 'payu_paisa',
      
      // Add Merchant ID if available
      ...(config.merchantId && { mid: config.merchantId })
    };

    // Validate hash parameters
    const hashValidation = validateHashParams(paymentData);
    if (!hashValidation.isValid) {
      logError('Hash parameter validation failed', { errors: hashValidation.errors });
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Hash parameter validation failed',
          errors: hashValidation.errors
        })
      };
    }

    // Generate hash
    try {
      const hash = generatePaymentHash(paymentData, config.salt);
      paymentData.hash = hash;
    } catch (hashError) {
      logError('Hash generation failed', { error: hashError.message });
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Hash generation failed'
        })
      };
    }

    // Log successful initiation
    logPaymentInitiation(paymentData);

    debugLog('PayU payment initiated successfully', {
      txnid: paymentData.txnid,
      amount: paymentData.amount,
      environment: config.testMode ? 'test' : 'production'
    });

    // Return payment data for frontend to handle
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        message: 'Payment initiated successfully',
        data: {
          txnid: paymentData.txnid,
          hash: paymentData.hash,
          key: paymentData.key,
          amount: paymentData.amount,
          paymentUrl: config.paymentUrl,
          paymentData: paymentData
        }
      })
    };

  } catch (error) {
    logError('PayU payment initiation error', {
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    });

    // Provide more specific error messages based on error type
    let errorMessage = 'Payment initiation failed. Please try again.';
    let statusCode = 500;

    if (process.env.NODE_ENV === 'development') {
      errorMessage = error.message || 'Payment initiation failed';
    } else {
      // Production error messages - more user-friendly
      if (error.message.includes('credentials') || error.message.includes('configuration')) {
        errorMessage = 'Payment service is temporarily unavailable. Please try again later or contact support.';
      } else if (error.message.includes('validation')) {
        errorMessage = 'Invalid payment information. Please check your details and try again.';
        statusCode = 400;
      } else if (error.message.includes('hash')) {
        errorMessage = 'Payment security validation failed. Please try again.';
      }
    }

    return {
      statusCode: statusCode,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: errorMessage,
        timestamp: new Date().toISOString(),
        ...(process.env.NODE_ENV === 'development' && {
          error: error.message,
          stack: error.stack
        })
      })
    };
  }
};
