/**
 * Receipt Generator Component
 * Generates properly formatted A4 PDF receipts for payment confirmations
 * Updated to match the design in the reference image
 */

import { forwardRef, useImperativeHandle } from 'react';
import { Button } from '@mui/material';
import { Download as DownloadIcon } from '@mui/icons-material';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { ReceiptData } from '../services/receiptService';

interface ReceiptGeneratorProps {
  receiptData: ReceiptData;
  onGenerated?: (pdfBlob: Blob) => void;
  onError?: (error: string) => void;
}

export interface ReceiptGeneratorRef {
  downloadPDF: () => Promise<void>;
}

const ReceiptGenerator = forwardRef<ReceiptGeneratorRef, ReceiptGeneratorProps>(({
  receiptData,
  onGenerated,
  onError
}, ref) => {
  const generatePDF = async () => {
    try {
      const receiptElement = document.createElement('div');
      receiptElement.style.cssText = `
        width: 794px;
        min-height: 1123px;
        padding: 40px;
        margin: 0;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 14px;
        line-height: 1.5;
        color: #000000;
        background-color: #ffffff;
        position: fixed;
        left: -9999px;
        top: 0;
        box-sizing: border-box;
        overflow: visible;
        z-index: -1;
      `;

      receiptElement.innerHTML = generateReceiptHTML(receiptData);
      document.body.appendChild(receiptElement);
      await new Promise(resolve => setTimeout(resolve, 500));

      const canvas = await html2canvas(receiptElement, {
        scale: 2,
        useCORS: true,
        allowTaint: false,
        backgroundColor: '#ffffff',
        width: 794,
        height: 1123,
        logging: false,
        removeContainer: true,
        imageTimeout: 15000,
        foreignObjectRendering: false,
        scrollX: 0,
        scrollY: 0,
        windowWidth: 1200,
        windowHeight: 1600,
        ignoreElements: (element) => {
          return element.tagName === 'SCRIPT' || element.tagName === 'STYLE';
        }
      });

      document.body.removeChild(receiptElement);

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        compress: true
      });

      const pageWidth = 210;
      const pageHeight = 297;
      const margin = 10;
      const contentWidth = pageWidth - (2 * margin);
      const contentHeight = pageHeight - (2 * margin);

      const imgAspectRatio = canvas.width / canvas.height;
      const contentAspectRatio = contentWidth / contentHeight;

      let imgWidth, imgHeight, xOffset, yOffset;

      if (imgAspectRatio > contentAspectRatio) {
        imgWidth = contentWidth;
        imgHeight = contentWidth / imgAspectRatio;
        xOffset = margin;
        yOffset = margin + (contentHeight - imgHeight) / 2;
      } else {
        imgHeight = contentHeight;
        imgWidth = contentHeight * imgAspectRatio;
        xOffset = margin + (contentWidth - imgWidth) / 2;
        yOffset = margin;
      }

      const imgData = canvas.toDataURL('image/jpeg', 0.95);
      pdf.addImage(imgData, 'JPEG', xOffset, yOffset, imgWidth, imgHeight);

      // Add metadata to PDF
      pdf.setProperties({
        title: `Receipt_${receiptData.clientId}`,
        subject: 'Darvi Group Farmer Registration Receipt',
        author: 'Darvi Group',
        creator: 'Darvi Group Registration System',
        keywords: 'receipt, payment, farmer, registration, darvi group'
      });

      const pdfBlob = pdf.output('blob');

      if (onGenerated) {
        onGenerated(pdfBlob);
      }

      return pdfBlob;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate PDF';
      if (onError) {
        onError(errorMessage);
      }
      throw error;
    }
  };

  const downloadPDF = async () => {
    try {
      const pdfBlob = await generatePDF();
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Receipt_${receiptData.clientId}_${receiptData.customerName.replace(/\s+/g, '_')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      // Error downloading PDF
    }
  };

  // Expose downloadPDF method to parent component
  useImperativeHandle(ref, () => ({
    downloadPDF
  }));

  return (
    <Button
      onClick={downloadPDF}
      variant="contained"
      size="large"
      startIcon={<DownloadIcon />}
      sx={{
        borderRadius: '12px',
        px: { xs: 2, sm: 3 },
        py: { xs: 1.5, sm: 1.75 },
        fontSize: { xs: '0.875rem', sm: '1rem' },
        fontWeight: 600,
        textTransform: 'none',
        background: 'linear-gradient(45deg, #1B4C35 30%, #2E7D32 90%)',
        boxShadow: '0 4px 14px 0 rgba(27, 76, 53, 0.3)',
        transition: 'all 0.3s ease',
        minWidth: { xs: '200px', sm: '220px' },
        '&:hover': {
          background: 'linear-gradient(45deg, #0A3622 30%, #1B4C35 90%)',
          boxShadow: '0 6px 20px rgba(27, 76, 53, 0.4)',
          transform: 'translateY(-2px)',
        },
        '&:active': {
          transform: 'translateY(0)',
        }
      }}
    >
      Download Receipt PDF
    </Button>
  );
});

const generateReceiptHTML = (data: ReceiptData): string => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const time = date.toLocaleString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).toLowerCase();

    return `${day}/${month}/${year} ${time}`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatPaymentMethod = (method: string) => {
    const methodMap: Record<string, string> = {
      'CC': 'Credit Card',
      'DC': 'Debit Card',
      'NB': 'Net Banking',
      'UPI': 'UPI',
      'WALLET': 'Digital Wallet',
      'EMI': 'EMI',
      'CARDLESSEMI': 'Cardless EMI'
    };
    return methodMap[method?.toUpperCase()] || method || 'PayU';
  };

  return `
    <div style="width: 100%; margin: 0; padding: 30px; font-family: Arial, sans-serif; box-sizing: border-box; background-color: #ffffff; color: #000000; line-height: 1.4;">

      <!-- Header -->
      <div style="text-align: center; margin-bottom: 30px; padding: 20px; border: 2px solid #4CAF50;">
        <h1 style="color: #2E7D32; margin: 0 0 10px 0; font-size: 28px; font-weight: bold;">DARVI GROUP</h1>
        <p style="margin: 0 0 10px 0; font-size: 16px; color: #4CAF50; font-weight: bold;">Farmer Registration Payment Receipt</p>
        <p style="margin: 0; font-size: 14px; color: #666;">www.darvigroup.in | +91 99868 90777</p>
        <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">GST: 29ABCDE1234F1Z5</p>
      </div>

      <!-- Receipt Info -->
      <div style="margin-bottom: 25px; padding: 15px; background: #f9f9f9; border-left: 4px solid #4CAF50;">
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="width: 70%;">
              <p style="margin: 0; font-size: 16px; color: #2E7D32; font-weight: bold;">Receipt #: ${data.clientId}</p>
              <p style="margin: 5px 0 0 0; font-size: 14px; color: #666;">Date: ${formatDate(data.paymentDate)}</p>
            </td>
            <td style="width: 30%; text-align: right;">
              <div style="background: #4CAF50; color: white; padding: 8px 16px; display: inline-block; font-weight: bold;">PAID</div>
            </td>
          </tr>
        </table>
      </div>

      <!-- Customer Information -->
      <div style="margin-bottom: 25px;">
        <h3 style="color: #2E7D32; margin: 0 0 15px 0; font-size: 18px; font-weight: bold; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;">Customer Information</h3>
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold; width: 25%;">Name:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.customerName}</td>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold; width: 25%;">Phone:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.customerPhone}</td>
          </tr>
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Email:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.customerEmail}</td>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Address:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.customerAddress || 'Not provided'}</td>
          </tr>
          ${data.customerCity || data.customerPincode ? `
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">City:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.customerCity || 'Not provided'}</td>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Pincode:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.customerPincode || 'Not provided'}</td>
          </tr>
          ` : ''}
        </table>
      </div>

      <!-- Registration Details -->
      <div style="margin-bottom: 25px;">
        <h3 style="color: #2E7D32; margin: 0 0 15px 0; font-size: 18px; font-weight: bold; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;">Registration Details</h3>
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold; width: 25%;">Land Area:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.landArea || 'Not specified'} ${data.landArea ? 'acres' : ''}</td>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold; width: 25%;">Soil Type:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.soilType || 'Not specified'}</td>
          </tr>
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">District:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.district || 'Not specified'}</td>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Taluk:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.taluk || 'Not specified'}</td>
          </tr>
        </table>
      </div>

      <!-- Payment Details -->
      <div style="margin-bottom: 25px;">
        <h3 style="color: #2E7D32; margin: 0 0 15px 0; font-size: 18px; font-weight: bold; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;">Payment Details</h3>
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Registration Fee:</td>
            <td style="padding: 10px; border: 1px solid #ddd; text-align: right; font-weight: bold;">${formatCurrency(data.baseAmount || 4500)}</td>
          </tr>
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">GST (5%):</td>
            <td style="padding: 10px; border: 1px solid #ddd; text-align: right; font-weight: bold;">${formatCurrency(data.gstAmount || 225)}</td>
          </tr>
          ${data.platformFeeAmount > 0 ? `
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Platform Fee:</td>
            <td style="padding: 10px; border: 1px solid #ddd; text-align: right; font-weight: bold;">${formatCurrency(data.platformFeeAmount)}</td>
          </tr>
          ` : ''}
          <tr style="background: #4CAF50; color: white;">
            <td style="padding: 15px; border: 1px solid #ddd; font-weight: bold; font-size: 16px;">TOTAL AMOUNT PAID:</td>
            <td style="padding: 15px; border: 1px solid #ddd; text-align: right; font-weight: bold; font-size: 18px;">${formatCurrency(data.totalAmount || 4725)}</td>
          </tr>
        </table>
      </div>

      <!-- Transaction Details -->
      <div style="margin-bottom: 25px;">
        <h3 style="color: #2E7D32; margin: 0 0 15px 0; font-size: 18px; font-weight: bold; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;">Transaction Details</h3>
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold; width: 25%;">Transaction ID:</td>
            <td style="padding: 10px; border: 1px solid #ddd; font-family: monospace;">${data.transactionId}</td>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold; width: 25%;">Payment Status:</td>
            <td style="padding: 10px; border: 1px solid #ddd; color: #4CAF50; font-weight: bold;">${data.status.toUpperCase()}</td>
          </tr>
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">PayU Transaction ID:</td>
            <td style="padding: 10px; border: 1px solid #ddd; font-family: monospace;">${data.mihpayid || data.paymentId || 'N/A'}</td>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Payment Gateway:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.paymentGateway || 'PayU'}</td>
          </tr>
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Payment Method:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${formatPaymentMethod(data.paymentMethod)}</td>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Payment Date:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${formatDate(data.paymentDate)}</td>
          </tr>
          ${data.paymentMode && data.paymentMode !== data.paymentMethod ? `
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Payment Mode:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.paymentMode}</td>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;"></td>
            <td style="padding: 10px; border: 1px solid #ddd;"></td>
          </tr>
          ` : ''}
          ${data.bankReferenceNumber || data.additionalData?.bankReferenceNumber ? `
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Bank Reference:</td>
            <td style="padding: 10px; border: 1px solid #ddd; font-family: monospace;" colspan="3">${data.bankReferenceNumber || data.additionalData?.bankReferenceNumber}</td>
          </tr>
          ` : ''}
          ${data.additionalData?.bankCode ? `
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Bank Code:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.additionalData.bankCode}</td>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">Bank Name:</td>
            <td style="padding: 10px; border: 1px solid #ddd;">${data.additionalData?.bankName || 'Not specified'}</td>
          </tr>
          ` : ''}
          ${data.additionalData?.upiVirtualAddress ? `
          <tr>
            <td style="padding: 10px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">UPI ID:</td>
            <td style="padding: 10px; border: 1px solid #ddd; font-family: monospace; word-break: break-all;" colspan="3">${data.additionalData.upiVirtualAddress}</td>
          </tr>
          ` : ''}
        </table>
      </div>

      <!-- Footer -->
      <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f9f9f9; border: 1px solid #ddd;">
        <h3 style="margin: 0 0 10px 0; color: #2E7D32; font-size: 18px;">Thank you for choosing Darvi Group!</h3>
        <p style="margin: 0 0 15px 0; font-size: 14px; color: #666;">Your registration has been successfully completed.</p>
        <p style="margin: 0 0 10px 0; font-size: 12px; color: #666;">This is a computer-generated receipt. No signature required.</p>
        <p style="margin: 0 0 15px 0; font-size: 12px; color: #666;">Receipt generated on: ${formatDate(new Date().toISOString())}</p>

        <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-top: 15px;">
          <p style="margin: 0 0 10px 0; font-size: 14px; color: #2E7D32; font-weight: bold;">For support, contact us:</p>
          <p style="margin: 0 0 5px 0; font-size: 14px; color: #666;">Phone: +91 99868 90777</p>
          <p style="margin: 0 0 10px 0; font-size: 14px; color: #666;">Website: www.darvigroup.in</p>
          <p style="margin: 0; font-size: 12px; color: #666; font-style: italic;">Empowering farmers with sustainable agricultural solutions</p>
        </div>
      </div>

      <!-- Receipt ID -->
      <div style="text-align: center; margin-top: 15px; padding: 10px; background: #f0f0f0; border: 1px solid #ccc;">
        <p style="margin: 0; font-size: 11px; color: #666;">Receipt ID: ${data.clientId} | Generated: ${formatDate(new Date().toISOString())}</p>
      </div>
    </div>
  `;
};

export default ReceiptGenerator;