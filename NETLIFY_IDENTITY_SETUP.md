# Netlify Identity Setup Guide

## Quick Setup for CMS Access

### Step 1: Enable Netlify Identity

1. Go to your Netlify Dashboard
2. Select your site (darvi-registration)
3. Go to **Site settings** → **Identity**
4. Click **Enable Identity**

### Step 2: Configure Identity Settings

1. **Registration preferences**: Set to "Invite only" for security
2. **External providers**: Enable Google, GitHub (optional)
3. **Git Gateway**: Enable this to allow CMS to commit changes

### Step 3: Invite Users

1. Go to **Identity** tab in your site dashboard
2. Click **Invite users**
3. Enter email addresses of people who should have CMS access
4. Users will receive an email to set up their password

### Step 4: Set User Roles (Optional)

1. After users accept invitations
2. Go to **Identity** → **Users**
3. Click on a user to edit their metadata
4. Add roles like:
   ```json
   {
     "roles": ["admin", "editor"]
   }
   ```

### Step 5: Test CMS Access

1. Visit `https://darvigroup.in/admin/` (production) or `http://localhost:8888/admin/` (development)
2. Click "Login with Netlify Identity"
3. Enter your credentials
4. Start editing content!

## Troubleshooting

### Common Issues

**1. "Login with Netlify Identity" button not working**
- Check that Identity is enabled in Netlify dashboard
- Verify the site URL matches your domain
- Clear browser cache and try again

**2. "Access denied" error**
- Make sure you've been invited as a user
- Check that Git Gateway is enabled
- Verify your email address matches the invitation

**3. Changes not saving**
- Ensure Git Gateway is properly configured
- Check that the user has write permissions
- Verify the repository connection

**4. CMS not loading**
- Check browser console for CSP errors
- Verify all Netlify CMS scripts are loading
- Try disabling browser extensions

### Development vs Production

**Development (localhost:8888)**
- Uses local backend for testing
- Changes are saved locally
- No authentication required for testing

**Production (darvigroup.in)**
- Requires Netlify Identity authentication
- Changes are committed to Git repository
- Triggers automatic deployments

## Security Best Practices

1. **Use "Invite only" registration**
2. **Set up user roles** for different access levels
3. **Enable 2FA** for admin users
4. **Regularly review** user access
5. **Monitor** CMS activity logs

## User Roles

### Admin
- Full access to all content and settings
- Can manage users and site configuration
- Access to payment and receipt settings

### Editor
- Can edit content but not settings
- Access to pages, posts, and media
- Cannot change site configuration

### Contributor
- Limited access to specific content areas
- Can create drafts but not publish
- Requires approval for changes

## Support

If you need help with Netlify Identity setup:
- Netlify Documentation: https://docs.netlify.com/visitor-access/identity/
- Netlify Support: https://www.netlify.com/support/
- Email: <EMAIL>
