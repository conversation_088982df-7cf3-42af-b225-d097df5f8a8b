# Production Environment Variables
# Set these in Netlify Dashboard > Site Settings > Environment Variables

# Google Apps Script URL for form submissions
REACT_APP_GOOGLE_SCRIPT_URL=https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec

# PayU Production Credentials
PAYU_MERCHANT_KEY=your_production_merchant_key
PAYU_MERCHANT_ID=your_production_merchant_id
PAYU_SALT_256=your_production_salt_256

# Production Domain
REACT_APP_DOMAIN=darvigroup.in
REACT_APP_BASE_URL=https://darvigroup.in

# Contact Information
REACT_APP_CONTACT_EMAIL=<EMAIL>
REACT_APP_CONTACT_NUMBER=+919986890777

# PayU Configuration
PAYU_TEST_MODE=false
PAYU_PAYMENT_URL=https://secure.payu.in/_payment

# Security Settings
REACT_APP_ENFORCE_HTTPS=true
REACT_APP_ENABLE_SECURITY=true

# Build Configuration
NODE_ENV=production
CI=false
SKIP_PREFLIGHT_CHECK=true
ESLINT_NO_DEV_ERRORS=true
GENERATE_SOURCEMAP=false
REACT_APP_ENVIRONMENT=production

# Success/Failure URLs
SUCCESS_URL=https://darvigroup.in/payment-success
FAILURE_URL=https://darvigroup.in/payment-failure

# Site Configuration
SITE_URL=https://darvigroup.in
CLIENT_URL=https://darvigroup.in
HOST_URL=https://darvigroup.in
