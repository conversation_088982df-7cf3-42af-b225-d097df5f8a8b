/**
 * PayU Payment Verification Function
 * Converted from PHP verify_payment.php to JavaScript Netlify Function
 * Verifies payment status using PayU's verification API
 */

// Import utilities
const { getPayUConfig } = require('./utils/payuConfig');
const { logPaymentVerification, logError, debugLog } = require('./utils/logger');

// CORS headers
const getCORSHeaders = (origin) => {
  const allowedOrigins = process.env.NODE_ENV === 'production' 
    ? ['https://darvigroup.in', 'https://www.darvigroup.in']
    : ['http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000'];
  
  const isAllowedOrigin = allowedOrigins.includes(origin);
  
  return {
    'Access-Control-Allow-Origin': isAllowedOrigin ? origin : allowedOrigins[0],
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Content-Type': 'application/json',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block'
  };
};

/**
 * Verify payment with PayU API
 * 
 * @param {string} txnid - Transaction ID to verify
 * @param {Object} config - PayU configuration
 * @returns {Object} - Verification result
 */
const verifyPaymentWithPayU = async (txnid, config) => {
  try {
    const verificationData = {
      key: config.merchantKey,
      command: 'verify_payment',
      var1: txnid,
      hash: require('crypto')
        .createHash('sha512')
        .update(`${config.merchantKey}|verify_payment|${txnid}|${config.salt}`)
        .digest('hex')
        .toLowerCase()
    };

    debugLog('Sending verification request to PayU', {
      txnid,
      verifyUrl: config.verifyUrl,
      key: config.merchantKey
    });

    const response = await fetch(config.verifyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Darvi Group Payment Verification'
      },
      body: new URLSearchParams(verificationData).toString()
    });

    if (!response.ok) {
      throw new Error(`PayU API request failed: ${response.status} ${response.statusText}`);
    }

    const responseText = await response.text();
    
    debugLog('PayU verification response received', {
      txnid,
      responseLength: responseText.length,
      responsePreview: responseText.substring(0, 100)
    });

    // Parse PayU response (usually JSON)
    let verificationResult;
    try {
      verificationResult = JSON.parse(responseText);
    } catch (parseError) {
      // If not JSON, try to parse as key-value pairs
      const lines = responseText.split('\n');
      verificationResult = {};
      lines.forEach(line => {
        const [key, value] = line.split('=');
        if (key && value) {
          verificationResult[key.trim()] = value.trim();
        }
      });
    }

    return {
      success: true,
      data: verificationResult,
      raw_response: responseText
    };

  } catch (error) {
    logError('PayU verification API error', {
      txnid,
      error: error.message
    });

    return {
      success: false,
      error: error.message,
      data: null
    };
  }
};

// Main handler
exports.handler = async (event) => {
  debugLog('PayU verify function called', {
    method: event.httpMethod,
    hasBody: !!event.body,
    hasQueryParams: !!event.queryStringParameters
  });

  const origin = event.headers.origin || event.headers.Origin;
  const corsHeaders = getCORSHeaders(origin);

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    const config = getPayUConfig();
    let txnid;

    // Get transaction ID from request
    if (event.httpMethod === 'POST' && event.body) {
      const requestData = JSON.parse(event.body);
      txnid = requestData.txnid;
    } else if (event.queryStringParameters) {
      txnid = event.queryStringParameters.txnid;
    }

    // Validate transaction ID
    if (!txnid || txnid.trim() === '') {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Transaction ID is required'
        })
      };
    }

    txnid = txnid.trim();

    debugLog('Verifying payment', { txnid });

    // Verify payment with PayU
    const verificationResult = await verifyPaymentWithPayU(txnid, config);

    if (!verificationResult.success) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Payment verification failed',
          error: verificationResult.error
        })
      };
    }

    // Process verification data
    const payuData = verificationResult.data;
    
    // Extract transaction details from PayU response
    const transactionDetails = {
      txnid,
      verification_status: 'completed',
      payu_response: payuData,
      verified_at: new Date().toISOString()
    };

    // Determine payment status from PayU response
    let paymentStatus = 'unknown';
    let paymentMessage = 'Payment status could not be determined';

    if (payuData.status) {
      paymentStatus = payuData.status.toLowerCase();
      switch (paymentStatus) {
        case 'success':
          paymentMessage = 'Payment completed successfully';
          break;
        case 'failure':
          paymentMessage = 'Payment failed';
          break;
        case 'pending':
          paymentMessage = 'Payment is pending';
          break;
        case 'cancel':
          paymentMessage = 'Payment was cancelled';
          break;
        default:
          paymentMessage = `Payment status: ${paymentStatus}`;
      }
    }

    // Log verification result
    logPaymentVerification(txnid, {
      status: paymentStatus,
      message: paymentMessage,
      payu_data: payuData
    });

    debugLog('Payment verification completed', {
      txnid,
      status: paymentStatus,
      message: paymentMessage
    });

    // Return verification result
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        message: 'Payment verification completed',
        data: {
          txnid,
          payment_status: paymentStatus,
          payment_message: paymentMessage,
          transaction_details: transactionDetails,
          payu_response: payuData
        }
      })
    };

  } catch (error) {
    logError('PayU verify handler error', {
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });

    const errorMessage = process.env.NODE_ENV === 'production'
      ? 'Payment verification failed. Please try again.'
      : error.message || 'Payment verification failed';

    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: errorMessage,
        timestamp: new Date().toISOString()
      })
    };
  }
};
