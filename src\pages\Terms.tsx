import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Chip,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import GavelIcon from '@mui/icons-material/Gavel';
import PrivacyTipIcon from '@mui/icons-material/PrivacyTip';
import MoneyOffIcon from '@mui/icons-material/MoneyOff';
import { useLanguage } from '../contexts/LanguageContext';
import translations from '../translations';

const Terms = () => {
  const { language } = useLanguage();
  const t = translations[language as keyof typeof translations].terms;
  const [expandedSection, setExpandedSection] = useState<string | false>('terms');

  const handleSectionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedSection(isExpanded ? panel : false);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      setExpandedSection(sectionId);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom color="primary" sx={{ fontWeight: 700 }}>
            {language === 'en' ? 'Legal Documentation' :
             language === 'kn' ? 'ಕಾನೂನು ದಾಖಲೆಗಳು' :
             'कानूनी दस्तावेज'}
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
            {language === 'en' ? 'Terms & Conditions, Privacy Policy, and Refund Policy' :
             language === 'kn' ? 'ನಿಯಮಗಳು ಮತ್ತು ಷರತ್ತುಗಳು, ಗೌಪ್ಯತಾ ನೀತಿ ಮತ್ತು ಮರುಪಾವತಿ ನೀತಿ' :
             'नियम और शर्तें, गोपनीयता नीति और धनवापसी नीति'}
          </Typography>

          {/* Table of Contents */}
          <Card sx={{ mb: 4, backgroundColor: '#f8f9fa' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, color: '#1B4C35' }}>
                {language === 'en' ? 'Quick Navigation' :
                 language === 'kn' ? 'ತ್ವರಿತ ನ್ಯಾವಿಗೇಶನ್' :
                 'त्वरित नेवीगेशन'}
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, justifyContent: 'center' }}>
                <Chip
                  icon={<GavelIcon />}
                  label={language === 'en' ? 'Terms & Conditions' :
                         language === 'kn' ? 'ನಿಯಮಗಳು ಮತ್ತು ಷರತ್ತುಗಳು' :
                         'नियम और शर्तें'}
                  onClick={() => scrollToSection('terms')}
                  variant={expandedSection === 'terms' ? 'filled' : 'outlined'}
                  color="primary"
                  sx={{ cursor: 'pointer' }}
                />
                <Chip
                  icon={<PrivacyTipIcon />}
                  label={language === 'en' ? 'Privacy Policy' :
                         language === 'kn' ? 'ಗೌಪ್ಯತಾ ನೀತಿ' :
                         'गोपनीयता नीति'}
                  onClick={() => scrollToSection('privacy')}
                  variant={expandedSection === 'privacy' ? 'filled' : 'outlined'}
                  color="primary"
                  sx={{ cursor: 'pointer' }}
                />
                <Chip
                  icon={<MoneyOffIcon />}
                  label={language === 'en' ? 'Refund Policy' :
                         language === 'kn' ? 'ಮರುಪಾವತಿ ನೀತಿ' :
                         'धनवापसी नीति'}
                  onClick={() => scrollToSection('refund')}
                  variant={expandedSection === 'refund' ? 'filled' : 'outlined'}
                  color="primary"
                  sx={{ cursor: 'pointer' }}
                />
              </Box>
            </CardContent>
          </Card>
        </Box>

        {/* Terms & Conditions Section */}
        <Accordion
          expanded={expandedSection === 'terms'}
          onChange={handleSectionChange('terms')}
          id="terms"
          sx={{ mb: 2 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={{ backgroundColor: '#f8f9fa' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <GavelIcon color="primary" />
              <Typography variant="h5" sx={{ fontWeight: 600, color: '#1B4C35' }}>
                {language === 'en' ? 'Terms & Conditions' :
                 language === 'kn' ? 'ನಿಯಮಗಳು ಮತ್ತು ಷರತ್ತುಗಳು' :
                 'नियम और शर्तें'}
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ mt: 2 }}>
              {/* Registration Program Terms */}
              <Typography variant="h6" gutterBottom color="primary">
                {language === 'en' ? 'Farmer Registration Program Terms' :
                 language === 'kn' ? 'ರೈತ ನೋಂದಣಿ ಕಾರ್ಯಕ್ರಮದ ನಿಯಮಗಳು' :
                 'किसान पंजीकरण कार्यक्रम नियम'}
              </Typography>
              <Typography paragraph>
                {language === 'en' ?
              'The Darvi Group Farmer Registration Program is a comprehensive agricultural support service offered for a one-time registration fee of ₹4725. This program provides lifetime membership benefits including free agricultural consultation, soil health assessment, customized crop recommendations, exclusive access to all Darvi Group products, 24/7 technical support, training workshop access, and market price updates. The registration fee is non-refundable once the consultation services begin.' :
             language === 'kn' ?
              'ದರ್ವಿ ಗುಂಪು ರೈತ ನೋಂದಣಿ ಕಾರ್ಯಕ್ರಮವು ₹4725 ಒಂದು ಬಾರಿ ನೋಂದಣಿ ಶುಲ್ಕಕ್ಕೆ ನೀಡಲಾಗುವ ಸಮಗ್ರ ಕೃಷಿ ಬೆಂಬಲ ಸೇವೆಯಾಗಿದೆ. ಈ ಕಾರ್ಯಕ್ರಮವು ಉಚಿತ ಕೃಷಿ ಸಲಹೆ, ಮಣ್ಣಿನ ಆರೋಗ್ಯ ಮೌಲ್ಯಮಾಪನ, ಕಸ್ಟಮೈಸ್ಡ್ ಬೆಳೆ ಶಿಫಾರಸುಗಳು, ಎಲ್ಲಾ ದರ್ವಿ ಗುಂಪು ಉತ್ಪನ್ನಗಳಿಗೆ ವಿಶೇಷ ಪ್ರವೇಶ, 24/7 ತಾಂತ್ರಿಕ ಬೆಂಬಲ, ತರಬೇತಿ ಕಾರ್ಯಾಗಾರ ಪ್ರವೇಶ ಮತ್ತು ಮಾರುಕಟ್ಟೆ ಬೆಲೆ ನವೀಕರಣಗಳು ಸೇರಿದಂತೆ ಜೀವಮಾನ ಸದಸ್ಯತ್ವ ಪ್ರಯೋಜನಗಳನ್ನು ಒದಗಿಸುತ್ತದೆ. ಸಲಹಾ ಸೇವೆಗಳು ಪ್ರಾರಂಭವಾದ ನಂತರ ನೋಂದಣಿ ಶುಲ್ಕವನ್ನು ಮರುಪಾವತಿ ಮಾಡಲಾಗುವುದಿಲ್ಲ.' :
              'दर्वी ग्रुप किसान पंजीकरण कार्यक्रम ₹4725 के एक बार पंजीकरण शुल्क के लिए प्रदान की जाने वाली एक व्यापक कृषि सहायता सेवा है। यह कार्यक्रम मुफ्त कृषि परामर्श, मिट्टी स्वास्थ्य मूल्यांकन, अनुकूलित फसल सिफारिशें, सभी दर्वी ग्रुप उत्पादों तक विशेष पहुंच, 24/7 तकनीकी सहायता, प्रशिक्षण कार्यशाला पहुंच, और बाजार मूल्य अपडेट सहित आजीवन सदस्यता लाभ प्रदान करता है। परामर्श सेवाएं शुरू होने के बाद पंजीकरण शुल्क वापसी योग्य नहीं है।'}
          </Typography>

          <Typography variant="h6" gutterBottom>
            {t.businessOverview.title}
          </Typography>
          <Typography paragraph>
            {t.businessOverview.content}
          </Typography>

          <Typography variant="h6" gutterBottom>
            {t.agriculturalSolutions.title}
          </Typography>
          <Typography paragraph>
            {t.agriculturalSolutions.content}
          </Typography>

          <Typography variant="h6" gutterBottom>
            {t.consultationServices.title}
          </Typography>
          <Typography paragraph>
            {t.consultationServices.content}
          </Typography>

          <Typography variant="h6" gutterBottom>
            {t.iotServices.title}
          </Typography>
          <Typography paragraph>
            {t.iotServices.content}
          </Typography>

          <Typography variant="h6" gutterBottom>
            {t.paymentTerms.title}
          </Typography>
          <Typography paragraph>
            {t.paymentTerms.content}
          </Typography>

              <Typography variant="h6" gutterBottom>
                {t.customerResponsibilities.title}
              </Typography>
              <Typography paragraph>
                {t.customerResponsibilities.content}
              </Typography>

              <Typography variant="h6" gutterBottom>
                {t.companyLiability.title}
              </Typography>
              <Typography paragraph>
                {t.companyLiability.content}
              </Typography>

              <Typography variant="h6" gutterBottom>
                {t.governingLaws.title}
              </Typography>
              <Typography paragraph>
                {t.governingLaws.content}
              </Typography>
            </Box>
          </AccordionDetails>
        </Accordion>

        {/* Privacy Policy Section */}
        <Accordion
          expanded={expandedSection === 'privacy'}
          onChange={handleSectionChange('privacy')}
          id="privacy"
          sx={{ mb: 2 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={{ backgroundColor: '#f8f9fa' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <PrivacyTipIcon color="primary" />
              <Typography variant="h5" sx={{ fontWeight: 600, color: '#1B4C35' }}>
                {language === 'en' ? 'Privacy Policy' :
                 language === 'kn' ? 'ಗೌಪ್ಯತಾ ನೀತಿ' :
                 'गोपनीयता नीति'}
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                {language === 'en' ? 'Business Information' :
                 language === 'kn' ? 'ವ್ಯಾಪಾರ ಮಾಹಿತಿ' :
                 'व्यापार जानकारी'}
              </Typography>
              <Typography paragraph>
                {language === 'en' ?
                  'Darvi Group is a registered business operating in Karnataka, India. We are a B2B and B2C provider of premium agricultural solutions, consultation services, and IoT-based smart farming solutions. This privacy policy applies to all our business operations, including our website and service delivery.' :
                 language === 'kn' ?
                  'ದರ್ವಿ ಗುಂಪು ಕರ್ನಾಟಕ, ಭಾರತದಲ್ಲಿ ಕಾರ್ಯನಿರ್ವಹಿಸುವ ನೋಂದಾಯಿತ ವ್ಯಾಪಾರವಾಗಿದೆ. ನಾವು ಪ್ರೀಮಿಯಂ ಕೃಷಿ ಪರಿಹಾರಗಳು, ಸಲಹಾ ಸೇವೆಗಳು ಮತ್ತು IoT-ಆಧಾರಿತ ಸ್ಮಾರ್ಟ್ ಫಾರ್ಮಿಂಗ್ ಪರಿಹಾರಗಳ B2B ಮತ್ತು B2C ಪೂರೈಕೆದಾರರಾಗಿದ್ದೇವೆ. ಈ ಗೌಪ್ಯತಾ ನೀತಿಯು ನಮ್ಮ ವೆಬ್‌ಸೈಟ್ ಮತ್ತು ಸೇವಾ ವಿತರಣೆ ಸೇರಿದಂತೆ ನಮ್ಮ ಎಲ್ಲಾ ವ್ಯಾಪಾರ ಕಾರ್ಯಾಚರಣೆಗಳಿಗೆ ಅನ್ವಯಿಸುತ್ತದೆ.' :
                  'दर्वी ग्रुप कर्नाटक, भारत में संचालित एक पंजीकृत व्यापार है। हम प्रीमियम कृषि समाधान, परामर्श सेवाओं और IoT-आधारित स्मार्ट फार्मिंग समाधानों के B2B और B2C प्रदाता हैं। यह गोपनीयता नीति हमारी वेबसाइट और सेवा वितरण सहित हमारे सभी व्यापारिक संचालन पर लागू होती है।'}
              </Typography>

              <Typography variant="h6" gutterBottom>
                {language === 'en' ? 'Information We Collect' :
                 language === 'kn' ? 'ನಾವು ಸಂಗ್ರಹಿಸುವ ಮಾಹಿತಿ' :
                 'हम जो जानकारी एकत्र करते हैं'}
              </Typography>
              <Typography paragraph>
                {language === 'en' ?
                  'We collect personal information including your name, mobile number, address, land details, and agricultural requirements when you register for our services. We also collect technical information such as IP address, browser type, and usage patterns to improve our services. All data collection is done with your explicit consent and for legitimate business purposes.' :
                 language === 'kn' ?
                  'ನೀವು ನಮ್ಮ ಸೇವೆಗಳಿಗೆ ನೋಂದಾಯಿಸಿಕೊಂಡಾಗ ನಾವು ನಿಮ್ಮ ಹೆಸರು, ಮೊಬೈಲ್ ಸಂಖ್ಯೆ, ವಿಳಾಸ, ಭೂಮಿ ವಿವರಗಳು ಮತ್ತು ಕೃಷಿ ಅವಶ್ಯಕತೆಗಳು ಸೇರಿದಂತೆ ವೈಯಕ್ತಿಕ ಮಾಹಿತಿಯನ್ನು ಸಂಗ್ರಹಿಸುತ್ತೇವೆ. ನಮ್ಮ ಸೇವೆಗಳನ್ನು ಸುಧಾರಿಸಲು ನಾವು IP ವಿಳಾಸ, ಬ್ರೌಸರ್ ಪ್ರಕಾರ ಮತ್ತು ಬಳಕೆಯ ಮಾದರಿಗಳಂತಹ ತಾಂತ್ರಿಕ ಮಾಹಿತಿಯನ್ನು ಸಹ ಸಂಗ್ರಹಿಸುತ್ತೇವೆ. ಎಲ್ಲಾ ಡೇಟಾ ಸಂಗ್ರಹಣೆಯು ನಿಮ್ಮ ಸ್ಪಷ್ಟ ಒಪ್ಪಿಗೆಯೊಂದಿಗೆ ಮತ್ತು ಕಾನೂನುಬದ್ಧ ವ್ಯಾಪಾರ ಉದ್ದೇಶಗಳಿಗಾಗಿ ಮಾಡಲಾಗುತ್ತದೆ.' :
                  'जब आप हमारी सेवाओं के लिए पंजीकरण करते हैं तो हम आपका नाम, मोबाइल नंबर, पता, भूमि विवरण और कृषि आवश्यकताओं सहित व्यक्तिगत जानकारी एकत्र करते हैं। हम अपनी सेवाओं को बेहतर बनाने के लिए IP पता, ब्राउज़र प्रकार और उपयोग पैटर्न जैसी तकनीकी जानकारी भी एकत्र करते हैं। सभी डेटा संग्रह आपकी स्पष्ट सहमति के साथ और वैध व्यापारिक उद्देश्यों के लिए किया जाता है।'}
              </Typography>

              <Typography variant="h6" gutterBottom>
                {language === 'en' ? 'Data Usage and Storage' :
                 language === 'kn' ? 'ಡೇಟಾ ಬಳಕೆ ಮತ್ತು ಸಂಗ್ರಹಣೆ' :
                 'डेटा उपयोग और भंडारण'}
              </Typography>
              <Typography paragraph>
                {language === 'en' ?
                  'Your data is stored securely and used for: (1) Processing and fulfilling service requests, (2) Providing agricultural consultation services, (3) Implementing and maintaining IoT solutions, (4) Improving our products and services, (5) Communicating about your orders, services, and account, (6) Sending relevant agricultural information and updates, and (7) Complying with legal requirements.' :
                 language === 'kn' ?
                  'ನಿಮ್ಮ ಡೇಟಾವನ್ನು ಸುರಕ್ಷಿತವಾಗಿ ಸಂಗ್ರಹಿಸಲಾಗುತ್ತದೆ ಮತ್ತು ಇವುಗಳಿಗಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ: (1) ಸೇವಾ ವಿನಂತಿಗಳನ್ನು ಪ್ರಕ್ರಿಯೆಗೊಳಿಸುವುದು ಮತ್ತು ಪೂರೈಸುವುದು, (2) ಕೃಷಿ ಸಲಹಾ ಸೇವೆಗಳನ್ನು ಒದಗಿಸುವುದು, (3) IoT ಪರಿಹಾರಗಳನ್ನು ಅನುಷ್ಠಾನಗೊಳಿಸುವುದು ಮತ್ತು ನಿರ್ವಹಿಸುವುದು, (4) ನಮ್ಮ ಉತ್ಪನ್ನಗಳು ಮತ್ತು ಸೇವೆಗಳನ್ನು ಸುಧಾರಿಸುವುದು, (5) ನಿಮ್ಮ ಆದೇಶಗಳು, ಸೇವೆಗಳು ಮತ್ತು ಖಾತೆಯ ಬಗ್ಗೆ ಸಂವಹನ ನಡೆಸುವುದು, (6) ಸಂಬಂಧಿತ ಕೃಷಿ ಮಾಹಿತಿ ಮತ್ತು ನವೀಕರಣಗಳನ್ನು ಕಳುಹಿಸುವುದು, ಮತ್ತು (7) ಕಾನೂನು ಅವಶ್ಯಕತೆಗಳನ್ನು ಅನುಸರಿಸುವುದು.' :
                  'आपका डेटा सुरक्षित रूप से संग्रहीत किया जाता है और इसका उपयोग निम्नलिखित के लिए किया जाता है: (1) सेवा अनुरोधों को संसाधित करना और पूरा करना, (2) कृषि परामर्श सेवाएं प्रदान करना, (3) IoT समाधानों को लागू करना और बनाए रखना, (4) हमारे उत्पादों और सेवाओं में सुधार करना, (5) आपके ऑर्डर, सेवाओं और खाते के बारे में संवाद करना, (6) प्रासंगिक कृषि जानकारी और अपडेट भेजना, और (7) कानूनी आवश्यकताओं का अनुपालन करना।'}
              </Typography>

              <Typography variant="h6" gutterBottom>
                {language === 'en' ? 'Contact Information' :
                 language === 'kn' ? 'ಸಂಪರ್ಕ ಮಾಹಿತಿ' :
                 'संपर्क जानकारी'}
              </Typography>
              <Typography paragraph>
                {language === 'en' ?
                  'If you have questions about our privacy practices or would like to exercise your data rights, please contact <NAME_EMAIL> or call +91-9986890777. Our physical address is #2 Totad building, Arjun Vihar Gokul road Hubli, Karnataka 580030, India.' :
                 language === 'kn' ?
                  'ನಮ್ಮ ಗೌಪ್ಯತಾ ಅಭ್ಯಾಸಗಳ ಬಗ್ಗೆ ನಿಮಗೆ ಪ್ರಶ್ನೆಗಳಿದ್ದರೆ ಅಥವಾ ನಿಮ್ಮ ಡೇಟಾ ಹಕ್ಕುಗಳನ್ನು ಚಲಾಯಿಸಲು ಬಯಸಿದರೆ, ದಯವಿಟ್ಟು <EMAIL> ನಲ್ಲಿ ನಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸಿ ಅಥವಾ +91-9986890777 ಗೆ ಕರೆ ಮಾಡಿ. ನಮ್ಮ ಭೌತಿಕ ವಿಳಾಸವು #2 ತೋಟದ್ ಕಟ್ಟಡ, ಅರ್ಜುನ್ ವಿಹಾರ್ ಗೋಕುಲ್ ರಸ್ತೆ ಹುಬ್ಬಳ್ಳಿ, ಕರ್ನಾಟಕ 580030, ಭಾರತ.' :
                  'यदि आपके पास हमारी गोपनीयता प्रथाओं के बारे में प्रश्न हैं या आप अपने डेटा अधिकारों का प्रयोग करना चाहते हैं, तो कृपया हमसे <EMAIL> पर संपर्क करें या +91-9986890777 पर कॉल करें। हमारा भौतिक पता #2 तोताद भवन, अर्जुन विहार गोकुल रोड हुबली, कर्नाटक 580030, भारत है।'}
              </Typography>
            </Box>
          </AccordionDetails>
        </Accordion>

        {/* Refund Policy Section */}
        <Accordion
          expanded={expandedSection === 'refund'}
          onChange={handleSectionChange('refund')}
          id="refund"
          sx={{ mb: 2 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={{ backgroundColor: '#f8f9fa' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <MoneyOffIcon color="primary" />
              <Typography variant="h5" sx={{ fontWeight: 600, color: '#1B4C35' }}>
                {language === 'en' ? 'Refund Policy' :
                 language === 'kn' ? 'ಮರುಪಾವತಿ ನೀತಿ' :
                 'धनवापसी नीति'}
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                {language === 'en' ? 'Agricultural Solutions' :
                 language === 'kn' ? 'ಕೃಷಿ ಪರಿಹಾರಗಳು' :
                 'कृषि समाधान'}
              </Typography>
              <Typography paragraph>
                {language === 'en' ?
                  'For agricultural solutions, we offer refunds or adjustments if the solutions do not meet our quality standards or are not as described. Once solutions are fully implemented, we cannot offer complete refunds as results depend on proper application and environmental factors.' :
                 language === 'kn' ?
                  'ಕೃಷಿ ಪರಿಹಾರಗಳಿಗಾಗಿ, ಪರಿಹಾರಗಳು ನಮ್ಮ ಗುಣಮಟ್ಟದ ಮಾನದಂಡಗಳನ್ನು ಪೂರೈಸದಿದ್ದರೆ ಅಥವಾ ವಿವರಿಸಿದಂತೆ ಇಲ್ಲದಿದ್ದರೆ ನಾವು ಮರುಪಾವತಿ ಅಥವಾ ಹೊಂದಾಣಿಕೆಗಳನ್ನು ನೀಡುತ್ತೇವೆ. ಪರಿಹಾರಗಳನ್ನು ಸಂಪೂರ್ಣವಾಗಿ ಅನುಷ್ಠಾನಗೊಳಿಸಿದ ನಂತರ, ಫಲಿತಾಂಶಗಳು ಸರಿಯಾದ ಅನ್ವಯ ಮತ್ತು ಪರಿಸರ ಅಂಶಗಳ ಮೇಲೆ ಅವಲಂಬಿತವಾಗಿರುವುದರಿಂದ ನಾವು ಸಂಪೂರ್ಣ ಮರುಪಾವತಿಯನ್ನು ನೀಡಲು ಸಾಧ್ಯವಿಲ್ಲ.' :
                  'कृषि समाधानों के लिए, यदि समाधान हमारे गुणवत्ता मानकों को पूरा नहीं करते हैं या वर्णित के अनुसार नहीं हैं तो हम धनवापसी या समायोजन की पेशकश करते हैं। एक बार समाधान पूरी तरह से लागू हो जाने के बाद, हम पूर्ण धनवापसी की पेशकश नहीं कर सकते क्योंकि परिणाम उचित अनुप्रयोग और पर्यावरणीय कारकों पर निर्भर करते हैं।'}
              </Typography>

              <Typography variant="h6" gutterBottom>
                {language === 'en' ? 'Registration Fee Refund Policy' :
                 language === 'kn' ? 'ನೋಂದಣಿ ಶುಲ್ಕ ಮರುಪಾವತಿ ನೀತಿ' :
                 'पंजीकरण शुल्क वापसी नीति'}
              </Typography>
              <Typography paragraph>
                {language === 'en' ?
                  'If refund is approved, your refund will be processed and a credit will automatically be applied to your original method of payment within 7-10 business days.' :
                 language === 'kn' ?
                  'ಮರುಪಾವತಿಯನ್ನು ಅನುಮೋದಿಸಿದರೆ, ನಿಮ್ಮ ಮರುಪಾವತಿಯನ್ನು ಪ್ರಕ್ರಿಯೆಗೊಳಿಸಲಾಗುತ್ತದೆ ಮತ್ತು 7-10 ವ್ಯಾಪಾರ ದಿನಗಳಲ್ಲಿ ನಿಮ್ಮ ಮೂಲ ಪಾವತಿ ವಿಧಾನಕ್ಕೆ ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಕ್ರೆಡಿಟ್ ಅನ್ನು ಅನ್ವಯಿಸಲಾಗುತ್ತದೆ.' :
                  'यदि धनवापसी स्वीकृत हो जाती है, तो आपकी धनवापसी संसाधित की जाएगी और 7-10 व्यावसायिक दिनों के भीतर आपकी मूल भुगतान विधि में स्वचालित रूप से क्रेडिट लागू हो जाएगा।'}
              </Typography>

              <Typography variant="h6" gutterBottom>
                {language === 'en' ? 'Consultation Services' :
                 language === 'kn' ? 'ಸಲಹಾ ಸೇವೆಗಳು' :
                 'परामर्श सेवाएं'}
              </Typography>
              <Typography paragraph>
                {language === 'en' ?
                  'For agricultural consultation services, refunds are available if the service is canceled before the scheduled consultation.' :
                 language === 'kn' ?
                  'ಕೃಷಿ ಸಲಹಾ ಸೇವೆಗಳಿಗಾಗಿ, ನಿಗದಿತ ಸಲಹೆಯ ಮೊದಲು ಸೇವೆಯನ್ನು ರದ್ದುಗೊಳಿಸಿದರೆ ಮರುಪಾವತಿ ಲಭ್ಯವಿದೆ.' :
                  'कृषि परामर्श सेवाओं के लिए, यदि निर्धारित परामर्श से पहले सेवा रद्द कर दी जाती है तो धनवापसी उपलब्ध है।'}
              </Typography>

              <Typography variant="h6" gutterBottom>
                {language === 'en' ? 'Contact for Refunds' :
                 language === 'kn' ? 'ಮರುಪಾವತಿಗಾಗಿ ಸಂಪರ್ಕಿಸಿ' :
                 'धनवापसी के लिए संपर्क'}
              </Typography>
              <Typography paragraph>
                {language === 'en' ?
                  'For refund requests, please contact our support <NAME_EMAIL> or call our helpline at +91-9986890777. Include your order/registration ID, payment details, and reason for refund in your request. Our customer service team will guide you through the refund process.' :
                 language === 'kn' ?
                  'ಮರುಪಾವತಿ ವಿನಂತಿಗಳಿಗಾಗಿ, ದಯವಿಟ್ಟು <EMAIL> ನಲ್ಲಿ ನಮ್ಮ ಬೆಂಬಲ ತಂಡವನ್ನು ಸಂಪರ್ಕಿಸಿ ಅಥವಾ +91-9986890777 ನಲ್ಲಿ ನಮ್ಮ ಸಹಾಯವಾಣಿಗೆ ಕರೆ ಮಾಡಿ. ನಿಮ್ಮ ವಿನಂತಿಯಲ್ಲಿ ನಿಮ್ಮ ಆದೇಶ/ನೋಂದಣಿ ID, ಪಾವತಿ ವಿವರಗಳು ಮತ್ತು ಮರುಪಾವತಿಯ ಕಾರಣವನ್ನು ಸೇರಿಸಿ. ನಮ್ಮ ಗ್ರಾಹಕ ಸೇವಾ ತಂಡವು ಮರುಪಾವತಿ ಪ್ರಕ್ರಿಯೆಯ ಮೂಲಕ ನಿಮಗೆ ಮಾರ್ಗದರ್ಶನ ನೀಡುತ್ತದೆ.' :
                  'धनवापसी अनुरोधों के लिए, कृपया हमारी सहायता टीम से <EMAIL> पर संपर्क करें या +91-9986890777 पर हमारी हेल्पलाइन पर कॉल करें। अपने अनुरोध में अपना ऑर्डर/पंजीकरण ID, भुगतान विवरण और धनवापसी का कारण शामिल करें। हमारी ग्राहक सेवा टीम धनवापसी प्रक्रिया के माध्यम से आपका मार्गदर्शन करेगी।'}
              </Typography>
            </Box>
          </AccordionDetails>
        </Accordion>
      </Paper>
    </Container>
  );
};

export default Terms;