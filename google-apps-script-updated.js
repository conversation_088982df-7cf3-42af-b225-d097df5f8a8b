/**
 * Updated Google Apps Script for Darvi Group Registration with PayU Payment Integration
 * Handles form submissions with payment information and client ID from frontend
 * Client ID is now generated on the frontend and passed to this script
 */

function doPost(e) {
  try {
    // Get the active spreadsheet
    var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();

    // Parse the incoming data - handle both parameter and postData formats
    var data;
    if (e.postData && e.postData.contents) {
      // Data sent in request body (current frontend implementation)
      data = JSON.parse(e.postData.contents);
    } else if (e.parameter && e.parameter.data) {
      // Data sent as URL parameter
      data = JSON.parse(e.parameter.data);
    } else {
      // Data sent directly as parameters
      data = e.parameter || {};
    }

    console.log('Received data:', JSON.stringify(data));

    // Get client ID from frontend data (no longer generated here)
    // This ensures consistency between receipt and Google Sheets
    var clientId = data.clientId || generateFallbackClientId();

    // Log the received client ID for debugging
    console.log('Received client ID from frontend:', clientId);

    // Get the current timestamp
    var timestamp = new Date().toISOString();

    // Ensure headers are set up
    setupSheetHeaders(sheet);

    // Prepare the row data matching updated form structure with payment info
    var rowData = [
      timestamp,                                    // Timestamp
      clientId,                                     // Client ID (from frontend)
      data.name || '',                             // Name
      data.mobile || '',                           // Mobile
      data.email || '',                            // Email
      data.address || '',                          // Address
      data.city || '',                             // City/Village
      data.state || 'Karnataka',                   // State (fixed)
      data.district || '',                         // District
      data.taluk || '',                            // Taluk
      data.landArea || '',                         // Land Area (in acres)
      data.soilType || '',                         // Soil Type
      data.irrigationFacilities ? 'Yes' : 'No',   // Irrigation Facilities
      data.termsAccepted ? 'Yes' : 'No',          // Terms Accepted
      data.paymentCompleted ? 'Yes' : 'No',       // Payment Completed
      data.transactionId || '',                    // Transaction ID (PayU txnid)
      data.paymentId || '',                        // Payment ID (PayU mihpayid)
      data.paymentAmount || '₹4725',               // Payment Amount
      data.paymentCompleted ? timestamp : '',      // Payment Date
      data.paymentMethod || 'PayU',                // Payment Method (PayU)
      'INR',                                       // Payment Currency
      data.paymentTimestamp || '',                 // Payment Timestamp from PayU
      data.verifiedByCallback ? 'Yes' : 'No',     // Verified by callback
      data.hashVerified ? 'Yes' : 'No'            // Hash verified
    ];

    // Append the row to the sheet
    sheet.appendRow(rowData);

    // Send confirmation email with improved PDF formatting
    sendConfirmationEmail(data, clientId);

    // Return success response
    return ContentService.createTextOutput(JSON.stringify({
      'status': 'success',
      'message': 'Data saved successfully',
      'clientId': clientId
    })).setMimeType(ContentService.MimeType.JSON);

  } catch(error) {
    // Log error for debugging
    console.error('Error in doPost:', error);

    // Return error response
    return ContentService.createTextOutput(JSON.stringify({
      'status': 'error',
      'message': error.toString()
    })).setMimeType(ContentService.MimeType.JSON);
  }
}

function doGet(e) {
  // Handle CORS preflight requests
  return ContentService.createTextOutput('OK')
    .setMimeType(ContentService.MimeType.TEXT);
}

/**
 * Generate fallback client ID if not provided by frontend
 * Format: DG + 4 random characters (uppercase letters and numbers)
 * This should rarely be used as frontend now generates consistent IDs
 */
function generateFallbackClientId() {
  var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  var result = 'DG';

  for (var i = 0; i < 4; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
}

/**
 * Set up sheet headers automatically if they don't exist
 * Updated headers to include payment information and client ID from frontend
 */
function setupSheetHeaders(sheet) {
  if (!sheet) {
    throw new Error("Sheet is undefined. Please check the sheet reference.");
  }

  var lastCol = sheet.getLastColumn();
  var firstRow = lastCol > 0 
    ? sheet.getRange(1, 1, 1, lastCol).getValues()[0] 
    : [];
  var hasHeaders = firstRow.length > 0 && firstRow.some(cell => cell !== '');

  if (!hasHeaders) {
    var headers = [
      'Timestamp',
      'Client ID',                    // Client ID from frontend
      'Name',
      'Mobile',
      'Email',                        // Email address
      'Address',
      'City/Village',
      'State',
      'District',
      'Taluk',
      'Land Area (acres)',
      'Soil Type',
      'Irrigation Facilities',
      'Terms Accepted',
      'Payment Completed',
      'Transaction ID',               // PayU txnid
      'Payment ID',                  // PayU mihpayid
      'Payment Amount',              // Amount paid
      'Payment Date',                // When payment was completed
      'Payment Method',              // PayU
      'Payment Currency',            // INR
      'Payment Timestamp',           // PayU payment timestamp
      'Verified by Callback',        // Whether payment was verified by callback
      'Hash Verified'                // Whether hash was verified
    ];

    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);

    var headerRange = sheet.getRange(1, 1, 1, headers.length);
    headerRange.setBackground('#f3f3f3');
    headerRange.setFontWeight('bold');
    headerRange.setBorder(true, true, true, true, true, true);

    sheet.autoResizeColumns(1, headers.length);
  }
}

/**
 * Send confirmation email with updated payment information and improved PDF formatting
 * Uses client ID from frontend instead of generating it
 * Optimized for A4 page size and proper formatting
 */
function sendConfirmationEmail(data, clientId) {
  var templateId = '1m-LZEjl5v8LOnNc2R9MyT4GjwPBRNZoYEq7MGlM8dP8'; // Replace with your Google Slides template ID
  var folderId = '1QGqFXJrSO2eYNvhVAqfqj714ialBbbJs'; // Replace with your folder ID where you want to store the PDF

  try {
    // Make a copy of the template
    var templateFile = DriveApp.getFileById(templateId);
    var presentationCopy = templateFile.makeCopy("Receipt_" + clientId + "_" + (data.name || 'Customer'), DriveApp.getFolderById(folderId));
    var presentationId = presentationCopy.getId();
    var presentation = SlidesApp.openById(presentationId);

    // Set the page size to A4 for proper formatting
    var pageWidth = 595.28; // A4 width in points (8.27 inches)
    var pageHeight = 841.89; // A4 height in points (11.69 inches)
    presentation.getPageWidth = pageWidth;
    presentation.getPageHeight = pageHeight;

    var slides = presentation.getSlides();

    // Create a mapping of placeholders to values with updated payment info
    var replacements = {
      "{{clientId}}": clientId,                    // Client ID from frontend
      "{{name}}": data.name || '',
      "{{mobile}}": data.mobile || '',
      "{{address}}": data.address || '',
      "{{city}}": data.city || '',
      "{{state}}": data.state || 'Karnataka',
      "{{district}}": data.district || '',
      "{{taluk}}": data.taluk || '',
      "{{landArea}}": data.landArea || '',
      "{{soilType}}": data.soilType || '',
      "{{irrigationFacilities}}": data.irrigationFacilities ? "Yes" : "No",
      "{{termsAccepted}}": data.termsAccepted ? "Yes" : "No",
      "{{paymentCompleted}}": data.paymentCompleted ? "Yes" : "No",
      "{{transactionId}}": data.transactionId || "N/A",
      "{{paymentId}}": data.paymentId || "N/A",
      "{{paymentAmount}}": data.paymentAmount || "₹4725",
      "{{paymentDate}}": data.paymentCompleted ? new Date().toLocaleDateString() : "N/A",
      "{{paymentMethod}}": data.paymentMethod || "PayU",
      "{{paymentCurrency}}": "INR",
      "{{paymentTimestamp}}": data.paymentTimestamp ? new Date(data.paymentTimestamp).toLocaleString() : "N/A",
      "{{verifiedByCallback}}": data.verifiedByCallback ? "Yes" : "No",
      "{{hashVerified}}": data.hashVerified ? "Yes" : "No"
    };

    // Replace placeholders in slides while preserving formatting
    for (var placeholder in replacements) {
      presentation.replaceAllText(placeholder, replacements[placeholder]);
    }

    // Save the changes
    presentation.saveAndClose();

    // Wait a moment for the changes to be saved
    Utilities.sleep(1000);

    // Convert the updated presentation to a PDF with optimized settings for A4
    var pdfBlob = DriveApp.getFileById(presentationId).getAs('application/pdf');
    pdfBlob.setName('Receipt_' + clientId + '_' + (data.name || 'Customer') + '.pdf');

    // Create email body with payment information
    var emailBody = "New registration received with payment:\n\n" +
                   "Client ID: " + clientId + " (Generated by Frontend)\n" +
                   "Name: " + (data.name || 'N/A') + "\n" +
                   "Mobile: " + (data.mobile || 'N/A') + "\n" +
                   "Payment Status: " + (data.paymentCompleted ? "Completed" : "Pending") + "\n" +
                   "Transaction ID: " + (data.transactionId || 'N/A') + "\n" +
                   "Payment Amount: " + (data.paymentAmount || '₹4725') + "\n" +
                   "Payment Method: " + (data.paymentMethod || 'PayU') + "\n" +
                   "Verified by Callback: " + (data.verifiedByCallback ? "Yes" : "No") + "\n" +
                   "Hash Verified: " + (data.hashVerified ? "Yes" : "No") + "\n\n" +
                   "Please find the registration confirmation attached.\n\n" +
                   "Note: Contact customer at " + (data.mobile || 'N/A') + " to provide confirmation details.";

    // Send the email with the PDF attachment to admin
    MailApp.sendEmail({
      to: '<EMAIL>', // Admin email - change this to your admin email
      subject: "New Registration with Payment - Client ID: " + clientId + " - " + (data.name || 'Customer'),
      body: emailBody,
      attachments: [pdfBlob]
    });

    // Optional: Delete the copied presentation after sending email
    DriveApp.getFileById(presentationId).setTrashed(true);

  } catch (emailError) {
    console.error('Error sending confirmation email:', emailError);
    // Don't throw error - registration should still succeed even if email fails
  }
}

/**
 * Manual function to set up the sheet headers (run this once if needed)
 */
function setupSheet() {
  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
  setupSheetHeaders(sheet);
}
