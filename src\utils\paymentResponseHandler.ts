/**
 * PayU Payment Response Handler
 * Handles payment responses from PayU redirects
 */

export interface PayUResponse {
  mihpayid?: string;
  mode?: string;
  status?: string;
  unmappedstatus?: string;
  key?: string;
  txnid?: string;
  amount?: string;
  cardCategory?: string;
  discount?: string;
  net_amount_debit?: string;
  addedon?: string;
  productinfo?: string;
  firstname?: string;
  lastname?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  country?: string;
  zipcode?: string;
  email?: string;
  phone?: string;
  udf1?: string;
  udf2?: string;
  udf3?: string;
  udf4?: string;
  udf5?: string;
  hash?: string;
  field1?: string;
  field2?: string;
  field3?: string;
  field4?: string;
  field5?: string;
  field6?: string;
  field7?: string;
  field8?: string;
  field9?: string;
  payment_source?: string;
  PG_TYPE?: string;
  bank_ref_num?: string;
  bankcode?: string;
  error?: string;
  error_Message?: string;
  name_on_card?: string;
  cardnum?: string;
  cardhash?: string;
}

export interface PaymentResult {
  isPaymentResponse: boolean;
  isSuccess: boolean;
  isFailure: boolean;
  transactionId?: string;
  amount?: string;
  paymentId?: string;
  errorMessage?: string;
  rawResponse?: PayUResponse;
}

class PaymentResponseHandler {
  /**
   * Check if current URL contains payment response parameters
   */
  static hasPaymentResponse(): boolean {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.has('payment') || urlParams.has('mihpayid') || urlParams.has('txnid');
  }

  /**
   * Parse payment response from URL parameters
   */
  static parsePaymentResponse(): PaymentResult {
    const urlParams = new URLSearchParams(window.location.search);
    
    // Check for simple payment status parameter
    const paymentStatus = urlParams.get('payment');
    if (paymentStatus) {
      return {
        isPaymentResponse: true,
        isSuccess: paymentStatus === 'success',
        isFailure: paymentStatus === 'failure',
        errorMessage: paymentStatus === 'failure' ? 'Payment was not completed' : undefined
      };
    }

    // Parse full PayU response parameters
    const response: PayUResponse = {};
    for (const [key, value] of urlParams.entries()) {
      (response as any)[key] = decodeURIComponent(value);
    }

    // Check if this is a PayU response
    if (!response.txnid && !response.mihpayid) {
      return {
        isPaymentResponse: false,
        isSuccess: false,
        isFailure: false
      };
    }

    // Determine success/failure based on PayU response
    const isSuccess = response.status === 'success';
    const isFailure = response.status === 'failure' || response.error !== 'E000';

    return {
      isPaymentResponse: true,
      isSuccess,
      isFailure,
      transactionId: response.txnid,
      amount: response.amount,
      paymentId: response.mihpayid,
      errorMessage: isFailure ? (response.error_Message || response.field9 || 'Payment failed') : undefined,
      rawResponse: response
    };
  }

  /**
   * Get stored form data from localStorage
   */
  static getStoredFormData(): any {
    try {
      const storedData = localStorage.getItem('payuCheckoutData');
      return storedData ? JSON.parse(storedData) : null;
    } catch (error) {
      // Error retrieving stored form data
      return null;
    }
  }

  /**
   * Clear stored form data and payment response from URL
   */
  static clearPaymentData(): void {
    try {
      // Clear localStorage
      localStorage.removeItem('payuCheckoutData');
      localStorage.removeItem('payuTransaction');
      
      // Clear URL parameters
      const url = new URL(window.location.href);
      const paramsToRemove = [
        'payment', 'mihpayid', 'mode', 'status', 'unmappedstatus', 'key', 'txnid',
        'amount', 'cardCategory', 'discount', 'net_amount_debit', 'addedon',
        'productinfo', 'firstname', 'lastname', 'address1', 'address2', 'city',
        'state', 'country', 'zipcode', 'email', 'phone', 'udf1', 'udf2', 'udf3',
        'udf4', 'udf5', 'hash', 'field1', 'field2', 'field3', 'field4', 'field5',
        'field6', 'field7', 'field8', 'field9', 'payment_source', 'PG_TYPE',
        'bank_ref_num', 'bankcode', 'error', 'error_Message', 'name_on_card',
        'cardnum', 'cardhash'
      ];

      paramsToRemove.forEach(param => {
        url.searchParams.delete(param);
      });

      // Update URL without page reload
      window.history.replaceState({}, document.title, url.toString());
    } catch (error) {
      // Error clearing payment data
    }
  }

  /**
   * Validate payment response hash (for security)
   */
  static async validatePaymentResponse(response: PayUResponse): Promise<boolean> {
    try {
      // This should ideally be done on the server side
      // For now, we'll do basic validation
      if (!response.txnid || !response.status || !response.hash) {
        return false;
      }

      // Additional validation can be added here
      return true;
    } catch (error) {
      // Error validating payment response
      return false;
    }
  }

  /**
   * Get user-friendly error message based on PayU error code
   */
  static getErrorMessage(errorCode?: string, errorMessage?: string): string {
    const errorMessages: Record<string, string> = {
      'E000': 'No Error',
      'E001': 'Unauthorized Payment Mode',
      'E002': 'Invalid Key',
      'E003': 'Invalid Amount',
      'E004': 'Invalid Transaction ID',
      'E005': 'Invalid Hash',
      'E006': 'Invalid Product Info',
      'E007': 'Invalid First Name',
      'E008': 'Invalid Email',
      'E009': 'Invalid Phone Number',
      'E010': 'Payment Failed',
      'E011': 'Payment Cancelled',
      'E012': 'Payment Declined'
    };

    if (errorCode && errorMessages[errorCode]) {
      return errorMessages[errorCode];
    }

    return errorMessage || 'Payment could not be completed';
  }

  /**
   * Format payment details for display
   */
  static formatPaymentDetails(result: PaymentResult): {
    transactionId: string;
    amount: string;
    status: string;
    paymentMethod?: string;
    timestamp?: string;
  } {
    return {
      transactionId: result.transactionId || 'N/A',
      amount: result.amount ? `₹${result.amount}` : 'N/A',
      status: result.isSuccess ? 'Success' : 'Failed',
      paymentMethod: result.rawResponse?.mode || 'N/A',
      timestamp: result.rawResponse?.addedon || new Date().toLocaleString()
    };
  }
}

export default PaymentResponseHandler;
