/**
 * Payment Failure Page
 * Displays payment failure information and retry options
 */

import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Error as ErrorIcon,
  Home as HomeIcon,
  Refresh as RefreshIcon,
  Phone as PhoneIcon,
  CheckCircle as CheckIcon,
  CreditCard as CardIcon,
  AccountBalance as BankIcon,
  Smartphone as UpiIcon
} from '@mui/icons-material';

import { useLanguage } from '../contexts/LanguageContext';
import config from '../config';

const PaymentFailure: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { language } = useLanguage();
  
  const [failureReason, setFailureReason] = useState<string>('');
  const [transactionId, setTransactionId] = useState<string>('');

  // Translations
  const translations = {
    en: {
      title: 'Payment Failed',
      subtitle: 'Your payment could not be processed',
      failureReason: 'Failure Reason',
      transactionId: 'Transaction ID',
      commonReasons: 'Common reasons for payment failure:',
      reason1: 'Insufficient balance in your account',
      reason2: 'Network connectivity issues',
      reason3: 'Incorrect card details or expired card',
      reason4: 'Transaction limit exceeded',
      reason5: 'Bank server temporarily unavailable',
      whatToDo: 'What can you do?',
      action1: 'Check your account balance and try again',
      action2: 'Verify your card details are correct',
      action3: 'Try using a different payment method',
      action4: 'Contact your bank if the issue persists',
      paymentMethods: 'Available Payment Methods',
      cards: 'Credit/Debit Cards',
      netBanking: 'Net Banking',
      upi: 'UPI Payments',
      wallets: 'Digital Wallets',
      tryAgain: 'Try Payment Again',
      backToHome: 'Back to Home',
      contactSupport: 'Contact Support',
      supportMessage: 'If you continue to face issues, please contact our support team:',
      amount: 'Amount',
      noCharges: 'No charges were deducted from your account for this failed transaction.',
      securePayment: 'All payments are processed securely through PayU payment gateway.'
    },
    hi: {
      title: 'भुगतान विफल',
      subtitle: 'आपका भुगतान संसाधित नहीं हो सका',
      failureReason: 'विफलता का कारण',
      transactionId: 'लेनदेन आईडी',
      commonReasons: 'भुगतान विफलता के सामान्य कारण:',
      reason1: 'आपके खाते में अपर्याप्त शेष राशि',
      reason2: 'नेटवर्क कनेक्टिविटी की समस्याएं',
      reason3: 'गलत कार्ड विवरण या समाप्त हो गया कार्ड',
      reason4: 'लेनदेन सीमा पार हो गई',
      reason5: 'बैंक सर्वर अस्थायी रूप से अनुपलब्ध',
      whatToDo: 'आप क्या कर सकते हैं?',
      action1: 'अपने खाते की शेष राशि जांचें और पुनः प्रयास करें',
      action2: 'सत्यापित करें कि आपके कार्ड विवरण सही हैं',
      action3: 'एक अलग भुगतान विधि का उपयोग करने का प्रयास करें',
      action4: 'यदि समस्या बनी रहती है तो अपने बैंक से संपर्क करें',
      paymentMethods: 'उपलब्ध भुगतान विधियां',
      cards: 'क्रेडिट/डेबिट कार्ड',
      netBanking: 'नेट बैंकिंग',
      upi: 'UPI भुगतान',
      wallets: 'डिजिटल वॉलेट',
      tryAgain: 'भुगतान पुनः प्रयास करें',
      backToHome: 'होम पर वापस',
      contactSupport: 'सहायता से संपर्क करें',
      supportMessage: 'यदि आपको समस्याओं का सामना करना पड़ता है, तो कृपया हमारी सहायता टीम से संपर्क करें:',
      amount: 'राशि',
      noCharges: 'इस असफल लेनदेन के लिए आपके खाते से कोई शुल्क नहीं काटा गया है।',
      securePayment: 'सभी भुगतान PayU पेमेंट गेटवे के माध्यम से सुरक्षित रूप से संसाधित होते हैं।'
    },
    kn: {
      title: 'ಪಾವತಿ ವಿಫಲವಾಗಿದೆ',
      subtitle: 'ನಿಮ್ಮ ಪಾವತಿಯನ್ನು ಸಂಸ್ಕರಿಸಲು ಸಾಧ್ಯವಾಗಲಿಲ್ಲ',
      failureReason: 'ವಿಫಲತೆಯ ಕಾರಣ',
      transactionId: 'ವಹಿವಾಟು ಐಡಿ',
      commonReasons: 'ಪಾವತಿ ವಿಫಲತೆಯ ಸಾಮಾನ್ಯ ಕಾರಣಗಳು:',
      reason1: 'ನಿಮ್ಮ ಖಾತೆಯಲ್ಲಿ ಸಾಕಷ್ಟು ಬ್ಯಾಲೆನ್ಸ್ ಇಲ್ಲ',
      reason2: 'ನೆಟ್‌ವರ್ಕ್ ಸಂಪರ್ಕ ಸಮಸ್ಯೆಗಳು',
      reason3: 'ತಪ್ಪಾದ ಕಾರ್ಡ್ ವಿವರಗಳು ಅಥವಾ ಅವಧಿ ಮೀರಿದ ಕಾರ್ಡ್',
      reason4: 'ವಹಿವಾಟು ಮಿತಿ ಮೀರಿದೆ',
      reason5: 'ಬ್ಯಾಂಕ್ ಸರ್ವರ್ ತಾತ್ಕಾಲಿಕವಾಗಿ ಲಭ್ಯವಿಲ್ಲ',
      whatToDo: 'ನೀವು ಏನು ಮಾಡಬಹುದು?',
      action1: 'ನಿಮ್ಮ ಖಾತೆಯ ಬ್ಯಾಲೆನ್ಸ್ ಪರಿಶೀಲಿಸಿ ಮತ್ತು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ',
      action2: 'ನಿಮ್ಮ ಕಾರ್ಡ್ ವಿವರಗಳು ಸರಿಯಾಗಿವೆ ಎಂದು ಪರಿಶೀಲಿಸಿ',
      action3: 'ವಿಭಿನ್ನ ಪಾವತಿ ವಿಧಾನವನ್ನು ಬಳಸಲು ಪ್ರಯತ್ನಿಸಿ',
      action4: 'ಸಮಸ್ಯೆ ಮುಂದುವರಿದರೆ ನಿಮ್ಮ ಬ್ಯಾಂಕ್ ಅನ್ನು ಸಂಪರ್ಕಿಸಿ',
      paymentMethods: 'ಲಭ್ಯವಿರುವ ಪಾವತಿ ವಿಧಾನಗಳು',
      cards: 'ಕ್ರೆಡಿಟ್/ಡೆಬಿಟ್ ಕಾರ್ಡ್‌ಗಳು',
      netBanking: 'ನೆಟ್ ಬ್ಯಾಂಕಿಂಗ್',
      upi: 'UPI ಪಾವತಿಗಳು',
      wallets: 'ಡಿಜಿಟಲ್ ವಾಲೆಟ್‌ಗಳು',
      tryAgain: 'ಪಾವತಿ ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ',
      backToHome: 'ಮುಖ್ಯ ಪುಟಕ್ಕೆ ಹಿಂತಿರುಗಿ',
      contactSupport: 'ಬೆಂಬಲವನ್ನು ಸಂಪರ್ಕಿಸಿ',
      supportMessage: 'ನೀವು ಸಮಸ್ಯೆಗಳನ್ನು ಎದುರಿಸುತ್ತಿದ್ದರೆ, ದಯವಿಟ್ಟು ನಮ್ಮ ಬೆಂಬಲ ತಂಡವನ್ನು ಸಂಪರ್ಕಿಸಿ:',
      amount: 'ಮೊತ್ತ',
      noCharges: 'ಈ ವಿಫಲವಾದ ವಹಿವಾಟಿಗಾಗಿ ನಿಮ್ಮ ಖಾತೆಯಿಂದ ಯಾವುದೇ ಶುಲ್ಕವನ್ನು ಕಡಿತಗೊಳಿಸಲಾಗಿಲ್ಲ.',
      securePayment: 'ಎಲ್ಲಾ ಪಾವತಿಗಳನ್ನು PayU ಪೇಮೆಂಟ್ ಗೇಟ್‌ವೇ ಮೂಲಕ ಸುರಕ್ಷಿತವಾಗಿ ಸಂಸ್ಕರಿಸಲಾಗುತ್ತದೆ.'
    }
  };

  const t = translations[language as keyof typeof translations];

  useEffect(() => {
    // Get failure information from URL parameters
    // const status = searchParams.get('status'); // Unused for now
    const txnid = searchParams.get('txnid');
    const message = searchParams.get('message');

    setTransactionId(txnid || 'N/A');
    setFailureReason(message || 'Payment was declined or cancelled');
  }, [searchParams]);

  const handleTryAgain = () => {
    // Navigate back to the form page with stored data
    navigate('/form', {
      state: {
        restoreFormData: true,
        paymentRetry: true
      }
    });
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleContactSupport = () => {
    // Open phone dialer
    window.location.href = 'tel:+91**********';
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        {/* Failure Header */}
        <Box textAlign="center" mb={4}>
          <ErrorIcon sx={{ fontSize: 80, color: 'error.main', mb: 2 }} />
          <Typography variant="h4" color="error.main" gutterBottom>
            {t.title}
          </Typography>
          <Typography variant="h6" color="text.secondary">
            {t.subtitle}
          </Typography>
        </Box>

        {/* Failure Details */}
        <Card elevation={1} sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t.failureReason}
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box mb={2}>
              <Typography variant="body1" color="error.main">
                {failureReason}
              </Typography>
            </Box>

            {transactionId !== 'N/A' && (
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">
                  {t.transactionId}: {transactionId}
                </Typography>
              </Box>
            )}

            <Box mb={2}>
              <Typography variant="body2" color="text.secondary">
                {t.amount}: {config.payu.paymentAmountDisplay}
              </Typography>
            </Box>

            <Alert severity="info" sx={{ mt: 2 }}>
              {t.noCharges}
            </Alert>
          </CardContent>
        </Card>

        {/* Common Reasons */}
        <Card elevation={1} sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t.commonReasons}
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <ErrorIcon color="error" />
                </ListItemIcon>
                <ListItemText primary={t.reason1} />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <ErrorIcon color="error" />
                </ListItemIcon>
                <ListItemText primary={t.reason2} />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <ErrorIcon color="error" />
                </ListItemIcon>
                <ListItemText primary={t.reason3} />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <ErrorIcon color="error" />
                </ListItemIcon>
                <ListItemText primary={t.reason4} />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <ErrorIcon color="error" />
                </ListItemIcon>
                <ListItemText primary={t.reason5} />
              </ListItem>
            </List>
          </CardContent>
        </Card>

        {/* What to Do */}
        <Card elevation={1} sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t.whatToDo}
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <CheckIcon color="success" />
                </ListItemIcon>
                <ListItemText primary={t.action1} />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckIcon color="success" />
                </ListItemIcon>
                <ListItemText primary={t.action2} />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckIcon color="success" />
                </ListItemIcon>
                <ListItemText primary={t.action3} />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckIcon color="success" />
                </ListItemIcon>
                <ListItemText primary={t.action4} />
              </ListItem>
            </List>
          </CardContent>
        </Card>

        {/* Available Payment Methods */}
        <Card elevation={1} sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t.paymentMethods}
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <CardIcon color="primary" />
                </ListItemIcon>
                <ListItemText primary={t.cards} />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <BankIcon color="primary" />
                </ListItemIcon>
                <ListItemText primary={t.netBanking} />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <UpiIcon color="primary" />
                </ListItemIcon>
                <ListItemText primary={t.upi} />
              </ListItem>
            </List>
          </CardContent>
        </Card>

        {/* Support Information */}
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2" gutterBottom>
            {t.supportMessage}
          </Typography>
          <Typography variant="h6" color="primary">
            <PhoneIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            +91 **********
          </Typography>
        </Alert>

        {/* Security Notice */}
        <Alert severity="info" sx={{ mb: 3 }}>
          {t.securePayment}
        </Alert>

        {/* Action Buttons */}
        <Box display="flex" justifyContent="center" gap={2} flexWrap="wrap">
          <Button
            variant="contained"
            onClick={handleTryAgain}
            startIcon={<RefreshIcon />}
            size="large"
          >
            {t.tryAgain}
          </Button>
          <Button
            variant="outlined"
            onClick={handleContactSupport}
            startIcon={<PhoneIcon />}
            size="large"
          >
            {t.contactSupport}
          </Button>
          <Button
            variant="outlined"
            onClick={handleBackToHome}
            startIcon={<HomeIcon />}
            size="large"
          >
            {t.backToHome}
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default PaymentFailure;
