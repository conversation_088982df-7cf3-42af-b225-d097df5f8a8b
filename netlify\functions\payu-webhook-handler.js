/**
 * PayU Webhook Handler
 * Handles PayU webhook notifications for payment status updates
 * Provides additional reliability for payment processing
 */

// Import utilities
const { getPayUConfig } = require('./utils/payuConfig');
const { verifyPayUHash } = require('./utils/hashGenerator');
const { validatePayUResponse, sanitizePayUResponse } = require('./utils/payuValidator');
const { logPaymentResponse, logError } = require('./utils/logger');

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Content-Type': 'application/json'
};

/**
 * Main webhook handler
 */
exports.handler = async (event) => {

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({ message: 'CORS preflight handled' })
    };
  }

  // Only accept POST requests for webhooks
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: 'Method not allowed. Only POST requests accepted.'
      })
    };
  }

  try {
    // Get PayU configuration
    const config = getPayUConfig();
    
    // Parse webhook data
    let webhookData = {};
    
    if (event.body) {
      try {
        // Try parsing as JSON first
        webhookData = JSON.parse(event.body);
      } catch (e) {
        // If not JSON, parse as form data
        const params = new URLSearchParams(event.body);
        webhookData = Object.fromEntries(params.entries());
      }
    }

    // Webhook data received and parsed

    // Validate webhook data
    const validation = validatePayUResponse(webhookData);
    if (!validation.isValid) {
      logError('Invalid webhook data received', {
        errors: validation.errors,
        receivedData: webhookData
      });
      
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Invalid webhook data',
          errors: validation.errors
        })
      };
    }

    // Sanitize webhook data
    const sanitized = sanitizePayUResponse(webhookData);
    
    // Verify webhook authenticity
    const hashVerification = verifyPayUHash(sanitized, config.salt);
    
    if (!hashVerification.isValid) {
      logError('Webhook hash verification failed', {
        txnid: sanitized.txnid,
        expectedHash: hashVerification.expectedHash,
        receivedHash: sanitized.hash,
        message: hashVerification.message
      });
      
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Webhook authentication failed'
        })
      };
    }

    // Process webhook based on status
    const result = await processWebhook(sanitized);
    
    // Log webhook processing
    logPaymentResponse(sanitized, result.success);

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        message: 'Webhook processed successfully',
        data: {
          txnid: sanitized.txnid,
          status: sanitized.status,
          processed_at: new Date().toISOString()
        }
      })
    };

  } catch (error) {
    logError('Webhook processing error', {
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });

    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        message: 'Webhook processing failed',
        timestamp: new Date().toISOString()
      })
    };
  }
};

/**
 * Process webhook based on payment status
 */
async function processWebhook(webhookData) {
  const { status, txnid } = webhookData;
  
  try {
    switch (status.toLowerCase()) {
      case 'success':
        return await handleSuccessWebhook(webhookData);
      
      case 'failure':
        return await handleFailureWebhook(webhookData);
      
      case 'pending':
        return await handlePendingWebhook(webhookData);
      
      case 'cancel':
      case 'cancelled':
        return await handleCancelWebhook(webhookData);
      
      default:
        return { success: true, message: 'Unknown status processed' };
    }
  } catch (error) {
    logError('Error processing webhook', {
      txnid,
      status,
      error: error.message
    });
    
    return { success: false, message: error.message };
  }
}

/**
 * Handle successful payment webhook
 */
async function handleSuccessWebhook() {
  // Here you could:
  // 1. Update database with payment confirmation
  // 2. Send confirmation emails
  // 3. Trigger business logic
  // 4. Update inventory or user accounts

  return {
    success: true,
    message: 'Success webhook processed',
    action: 'payment_confirmed'
  };
}

/**
 * Handle failed payment webhook
 */
async function handleFailureWebhook() {
  // Here you could:
  // 1. Update database with failure status
  // 2. Send failure notifications
  // 3. Trigger retry mechanisms
  // 4. Log for analytics

  return {
    success: true,
    message: 'Failure webhook processed',
    action: 'payment_failed'
  };
}

/**
 * Handle pending payment webhook
 */
async function handlePendingWebhook() {
  // Here you could:
  // 1. Update status to pending
  // 2. Set up monitoring for final status
  // 3. Send pending notifications

  return {
    success: true,
    message: 'Pending webhook processed',
    action: 'payment_pending'
  };
}

/**
 * Handle cancelled payment webhook
 */
async function handleCancelWebhook() {
  // Here you could:
  // 1. Update database with cancelled status
  // 2. Release reserved inventory
  // 3. Send cancellation notifications

  return {
    success: true,
    message: 'Cancel webhook processed',
    action: 'payment_cancelled'
  };
}


