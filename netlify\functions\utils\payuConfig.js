/**
 * PayU Configuration Utility
 * Converted from PHP config.php to JavaScript for Netlify Functions
 * Handles environment variables and PayU configuration
 */

// PayU Configuration
const getPayUConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isTestMode = process.env.PAYU_TEST_MODE === 'true';

  // Determine which credentials to use based on availability and test mode
  let merchantKey, merchantId, salt;

  if (isTestMode || (!process.env.PAYU_MERCHANT_KEY && !isProduction)) {
    // Use test credentials
    merchantKey = process.env.PAYU_TEST_KEY || 'kCDLd6';
    merchantId = process.env.PAYU_TEST_MERCHANT_ID || '13017075';
    salt = process.env.PAYU_TEST_SALT || 'ELd0paVbDhHaDfzpr/B2GBROiRZxoTj';
    console.log('🧪 Using PayU Test Credentials');
  } else {
    // Use production credentials
    merchantKey = process.env.PAYU_MERCHANT_KEY;
    merchantId = process.env.PAYU_MERCHANT_ID;
    salt = process.env.PAYU_SALT_256 || process.env.PAYU_SALT_32;
    console.log('🔐 Using PayU Production Credentials');
  }

  // Load configuration from environment variables
  const config = {
    // Merchant Details - Dynamic based on test mode and credential availability
    merchantKey: merchantKey,
    merchantId: merchantId,
    salt: salt,

    // Client Credentials - Optional for basic payments, required for advanced features
    clientId: process.env.PAYU_CLIENT_ID || '5f0458fc94005072bb08f32fb3427dd099ccb9a0acff22fe74a2e67bde87cf83',
    clientSecret: process.env.PAYU_CLIENT_SECRET || 'eeb1ce03b4fcba1a9a974a1eae71ec076710bc4f16cd41509acd6dd92aedbc21',

    // Environment URLs
    baseUrl: process.env.PAYU_BASE_URL || 'https://secure.payu.in',
    paymentUrl: process.env.PAYU_PAYMENT_URL || 'https://secure.payu.in/_payment',

    // Test mode setting - can be forced even in production for testing
    testMode: isTestMode || (!process.env.PAYU_MERCHANT_KEY && !isProduction),

    // Site URLs - Dynamic based on environment
    siteUrl: isProduction ? 'https://darvigroup.in' : 'http://localhost:3000',

    // PayU callback URLs - use Netlify routing for preprocessing
    successUrl: isProduction ? 'https://darvigroup.in/payu/success' : 'http://localhost:3000/payu/success',
    failureUrl: isProduction ? 'https://darvigroup.in/payu/failure' : 'http://localhost:3000/payu/failure',
    cancelUrl: isProduction ? 'https://darvigroup.in/payu/cancel' : 'http://localhost:3000/payu/cancel'
  };

  // If test mode is enabled, use test URLs regardless of environment
  if (config.testMode) {
    config.paymentUrl = 'https://test.payu.in/_payment';
    config.verifyUrl = 'https://test.payu.in/merchant/postservice.php?form=2';
    console.log('⚠️ PayU Test Mode Enabled - Using test URLs');
  } else {
    config.verifyUrl = 'https://info.payu.in/merchant/postservice.php?form=2';
  }

  // Validate required credentials
  const requiredFields = ['merchantKey', 'merchantId', 'salt'];
  const missingFields = requiredFields.filter(field => !config[field]);

  if (missingFields.length > 0) {
    const errorMsg = `Missing required PayU credentials: ${missingFields.join(', ')}. Please set environment variables: ${missingFields.map(f => `PAYU_${f.toUpperCase()}`).join(', ')}`;
    console.error('❌ PayU Configuration Error:', errorMsg);
    throw new Error(errorMsg);
  }

  // Smart validation based on credential availability and environment
  if (process.env.NODE_ENV === 'development') {
    if (isProduction && !config.testMode) {
      // In production with test mode disabled, warn if using test credentials but don't fail
      if (config.merchantKey === 'kCDLd6' || config.merchantId === '13017075') {
        console.warn('⚠️ Using test credentials in production. Consider setting production credentials or enabling test mode.');
        console.log('💡 To use production credentials, set: PAYU_MERCHANT_KEY, PAYU_MERCHANT_ID, PAYU_SALT_256');
        console.log('💡 To enable test mode in production, set: PAYU_TEST_MODE=true');
      } else {
        console.log('✅ PayU Production Mode - Using live credentials');
      }
    } else if (isProduction && config.testMode) {
      console.log('⚠️ PayU Test Mode in Production - Using test credentials for testing');
    } else if (config.testMode) {
      console.log('🧪 PayU Test Mode - Using test credentials');
    }

    // Log configuration (without sensitive data)
    console.log('PayU Configuration:', {
      environment: isProduction ? 'production' : 'development',
      testMode: config.testMode,
      merchantId: config.merchantId,
      paymentUrl: config.paymentUrl,
      hasCredentials: !!(config.merchantKey && config.salt)
    });
  }

  return config;
};

/**
 * Generate secure hash for PayU according to official documentation
 * Format: sha512(key|txnid|amount|productinfo|firstname|email|udf1|udf2|udf3|udf4|udf5||||||SALT)
 */
const generatePayUHash = (key, txnid, amount, productinfo, firstname, email, salt, udf1 = '', udf2 = '', udf3 = '', udf4 = '', udf5 = '') => {
  const crypto = require('crypto');
  
  // Construct hash string exactly as per PayU documentation
  const hashString = `${key}|${txnid}|${amount}|${productinfo}|${firstname}|${email}|${udf1}|${udf2}|${udf3}|${udf4}|${udf5}||||||${salt}`;
  
  // Generate SHA512 hash and return in lowercase
  return crypto.createHash('sha512').update(hashString).digest('hex').toLowerCase();
};

// Transaction ID generation moved to payuValidator.js to avoid duplication

/**
 * Verify response hash from PayU (Reverse Hash)
 * Format: sha512(SALT|status||||||udf5|udf4|udf3|udf2|udf1|email|firstname|productinfo|amount|txnid|key)
 */
const verifyResponseHash = (key, txnid, amount, productinfo, firstname, email, status, salt, udf1 = '', udf2 = '', udf3 = '', udf4 = '', udf5 = '') => {
  const crypto = require('crypto');
  
  // Construct reverse hash string exactly as per PayU documentation
  const hashString = `${salt}|${status}||||||${udf5}|${udf4}|${udf3}|${udf2}|${udf1}|${email}|${firstname}|${productinfo}|${amount}|${txnid}|${key}`;
  
  // Generate SHA512 hash and return in lowercase
  return crypto.createHash('sha512').update(hashString).digest('hex').toLowerCase();
};

/**
 * Log transaction for debugging - only in development
 */
const logTransaction = (data, type = 'info') => {
  if (process.env.NODE_ENV === 'development') {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      type,
      data
    };

    console.log(`[${timestamp}] [${type}]`, JSON.stringify(data));
    return logEntry;
  }

  return null;
};

/**
 * Validate required environment variables
 */
const validateEnvironment = () => {
  const config = getPayUConfig();
  const errors = [];
  
  if (!config.merchantKey) {
    errors.push('PAYU_MERCHANT_KEY is required');
  }
  
  if (!config.salt) {
    errors.push('PAYU_SALT_256 or PAYU_SALT_32 is required');
  }
  
  if (!config.merchantId) {
    errors.push('PAYU_MERCHANT_ID is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    config
  };
};

module.exports = {
  getPayUConfig,
  generatePayUHash,
  verifyResponseHash,
  logTransaction,
  validateEnvironment
};
