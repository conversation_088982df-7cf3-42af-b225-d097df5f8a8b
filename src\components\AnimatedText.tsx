import React, { useEffect, useRef } from 'react';
import { Typography, Box, TypographyProps } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';

interface AnimatedTextProps extends TypographyProps {
  text: string;
  delay?: number;
  duration?: number;
  color?: string;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'subtitle1' | 'subtitle2' | 'body1' | 'body2';
  component?: React.ElementType;
  animation?: 'fadeIn' | 'slideUp' | 'typewriter';
}

const fadeIn = keyframes`
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
`;

const slideUp = keyframes`
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
`;

const AnimatedBox = styled(Box)<{ duration: number; delay: number; animation: string }>(
  ({ duration, delay, animation }) => ({
    opacity: 0,
    animation: `${animation === 'fadeIn' ? fadeIn : slideUp} ${duration}s ease-out ${delay}s forwards`,
  })
);

// Create a blink animation
const blink = keyframes`
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
`;

const TypewriterText = styled(Typography)<{ duration: number; delay: number }>(
  ({ duration, delay }) => ({
    position: 'relative',
    '&::after': {
      content: '""',
      position: 'absolute',
      right: '-8px',
      top: '50%',
      transform: 'translateY(-50%)',
      height: '80%',
      width: '4px',
      backgroundColor: 'currentColor',
      animation: `${blink} 1s infinite`,
    },
  })
);

const AnimatedText: React.FC<AnimatedTextProps> = ({
  text,
  delay = 0,
  duration = 1,
  color,
  variant = 'body1',
  component,
  animation = 'fadeIn',
  ...props
}) => {
  const textRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (animation === 'typewriter' && textRef.current) {
      const element = textRef.current;
      const fullText = text;

      // Clear any existing content
      element.textContent = '';

      // Set a timeout based on the delay
      const timeout = setTimeout(() => {
        let currentIndex = 0;

        // Type each character with a small delay
        const interval = setInterval(() => {
          if (currentIndex < fullText.length) {
            element.textContent += fullText[currentIndex];
            currentIndex++;
          } else {
            clearInterval(interval);
          }
        }, (duration * 1000) / fullText.length);

        return () => clearInterval(interval);
      }, delay * 1000);

      return () => clearTimeout(timeout);
    }
  }, [text, delay, duration, animation]);

  if (animation === 'typewriter') {
    return (
      <TypewriterText
        variant={variant}
        color={color}
        duration={duration}
        delay={delay}
        {...props}
      >
        <span ref={textRef}></span>
      </TypewriterText>
    );
  }

  return (
    <AnimatedBox
      duration={duration}
      delay={delay}
      animation={animation}
    >
      <Typography
        variant={variant}
        component={component || 'div'}
        color={color}
        {...props}
      >
        {text}
      </Typography>
    </AnimatedBox>
  );
};

export default AnimatedText;
