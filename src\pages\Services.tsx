import React from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Paper,
  Chip,
  Button
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import StarIcon from '@mui/icons-material/Star';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import IconComponent from '../utils/iconUtils';
import servicesContent from '../content/services/content.json';

const Services = () => {

  const getIcon = (index: number) => {
    const iconNames = ['agriculture', 'science', 'support', 'trending', 'phone', 'school', 'chart', 'school'];
    return <IconComponent name={iconNames[index] || 'default'} size={40} />;
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography
          variant="h2"
          component="h1"
          gutterBottom
          sx={{
            fontWeight: 700,
            background: 'linear-gradient(45deg, #1B4C35 30%, #4CAF50 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 2
          }}
        >
          {servicesContent.hero.title}
        </Typography>
        <Typography variant="h5" color="text.secondary" paragraph sx={{ maxWidth: '800px', mx: 'auto' }}>
          {servicesContent.hero.subtitle}
        </Typography>

        {/* Pricing Display with CTA */}
        <Paper
          elevation={6}
          sx={{
            display: 'inline-block',
            p: 4,
            mt: 3,
            borderRadius: '20px',
            background: 'linear-gradient(135deg, #1B4C35 0%, #2E7D32 100%)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <Chip
            label={servicesContent.pricing.offerLabel}
            icon={<StarIcon />}
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              backgroundColor: '#FF6B35',
              color: 'white',
              fontWeight: 600
            }}
          />
          <Typography variant="h3" component="div" gutterBottom fontWeight={700}>
            ₹{servicesContent.pricing.amount.toLocaleString()}
          </Typography>
          <Typography variant="h6" sx={{ mb: 3, opacity: 0.9 }}>
            {servicesContent.pricing.description}
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="outlined"
              component={RouterLink}
              to="/checkout"
              size="large"
              endIcon={<ArrowForwardIcon />}
              sx={{
                borderColor: 'white',
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: '12px',
                '&:hover': {
                  backgroundColor: 'rgba(255,255,255,0.1)',
                  borderColor: 'white'
                }
              }}
            >
              Register Now
            </Button>
            <Button
              variant="text"
              component={RouterLink}
              to="/pricing"
              size="large"
              sx={{
                color: 'white',
                fontWeight: 600,
                px: 4,
                py: 1.5,
                borderRadius: '12px',
                '&:hover': {
                  backgroundColor: 'rgba(255,255,255,0.1)'
                }
              }}
            >
              View Pricing Details
            </Button>
          </Box>
        </Paper>
      </Box>





      {/* Services Benefits Section */}
      <Box sx={{ mb: 8 }}>
        <Typography variant="h4" gutterBottom align="center" sx={{ mb: 6, fontWeight: 600 }}>
          What You Get With Registration
        </Typography>
        <Grid container spacing={4}>
          {servicesContent.benefits.map((benefit, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Card
                elevation={3}
                sx={{
                  height: '100%',
                  borderRadius: '16px',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 16px 40px rgba(0,0,0,0.12)'
                  }
                }}
              >
                <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    {getIcon(index)}
                    <Chip
                      label={benefit.value}
                      size="small"
                      sx={{
                        ml: 'auto',
                        backgroundColor: '#E8F5E8',
                        color: '#1B4C35',
                        fontWeight: 600
                      }}
                    />
                  </Box>
                  <Typography variant="h6" gutterBottom fontWeight={600} color="primary">
                    {benefit.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1 }}>
                    {benefit.description}
                  </Typography>
                  <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                    <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20, mr: 1 }} />
                    <Typography variant="caption" color="success.main" fontWeight={600}>
                      Included in Registration
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Contact Information */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom align="center" sx={{ mb: 4 }}>
          Contact Information
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card elevation={2} sx={{ textAlign: 'center', p: 3 }}>
              <Box sx={{ mb: 2 }}>
                <IconComponent name="phone" size={40} />
              </Box>
              <Typography variant="h6" gutterBottom>
                Phone
              </Typography>
              <Typography variant="body1" color="primary">
                {servicesContent.contactInfo.phone}
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card elevation={2} sx={{ textAlign: 'center', p: 3 }}>
              <Box sx={{ mb: 2 }}>
                <IconComponent name="email" size={40} />
              </Box>
              <Typography variant="h6" gutterBottom>
                Email
              </Typography>
              <Typography variant="body1" color="primary">
                {servicesContent.contactInfo.email}
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card elevation={2} sx={{ textAlign: 'center', p: 3 }}>
              <Box sx={{ mb: 2 }}>
                <IconComponent name="location" size={40} />
              </Box>
              <Typography variant="h6" gutterBottom>
                Address
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {servicesContent.contactInfo.address}
              </Typography>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ textAlign: 'center', mt: 3 }}>
          <Typography variant="body1" color="text.secondary">
            <strong>Working Hours:</strong> {servicesContent.contactInfo.workingHours}
          </Typography>
        </Box>
      </Box>
    </Container>
  );
};

export default Services;
