# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules/
**/node_modules/
.pnp
.pnp.js

# Testing
coverage/

# Production
build/
dist/

# Misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
gen-lang-client-*.json

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.idea/
.vscode/
*.swp
*.swo

# TypeScript
*.tsbuildinfo

# Local Netlify folder
.netlify

# Documentation and temporary files
*.md.backup
*.tmp
*.temp
temp/
tmp/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Package manager lock files (keep package-lock.json for consistency)
yarn.lock
pnpm-lock.yaml

# Test files and mock data
**/__tests__/
**/*.test.ts
**/*.test.tsx
**/*.spec.ts
**/*.spec.tsx
test-*.js
mock-*.js

# Deployment and build artifacts
*.tgz
*.tar.gz

# Environment and config backups
.env.backup
config.backup.*
