# Netlify Configuration for Darvi Group Production Deployment
# Single deployment with Netlify Functions for darvigroup.in

[build]
  command = "npm install && npm run build"
  publish = "build"
  functions = "netlify/functions"

[build.environment]
  NODE_ENV = "production"
  CI = "false"
  SKIP_PREFLIGHT_CHECK = "true"
  ESLINT_NO_DEV_ERRORS = "true"
  GENERATE_SOURCEMAP = "false"
  NETLIFY_NEXT_PLUGIN_SKIP = "true"
  REACT_APP_ENVIRONMENT = "production"
  REACT_APP_DOMAIN = "darvigroup.in"



# This is a React app, not Next.js - Next.js plugin should be disabled via environment variable

# Security headers for production
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "geolocation=(), microphone=(), camera=()"

    # HTTPS enforcement
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"

    # Content Security Policy for production with PayU integration
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://secure.payu.in https://test.payu.in https://*.payu.in https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://darvigroup.in https://secure.payu.in https://test.payu.in https://*.payu.in https://info.payu.in https://script.google.com https://script.googleusercontent.com https://www.google-analytics.com https://analytics.google.com; frame-src 'self' https://secure.payu.in https://test.payu.in https://*.payu.in; object-src 'none'; base-uri 'self'; form-action 'self' https://secure.payu.in/_payment https://test.payu.in/_payment https://secure.payu.in https://test.payu.in https://*.payu.in;"

# Cache optimization for static assets
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"



# PayU Payment Callback Handlers (MUST BE FIRST)
# Handle POST requests from PayU
[[redirects]]
  from = "/payu/success"
  to = "/.netlify/functions/payu-callback-handler"
  status = 200
  force = true
  conditions = {Method = ["GET", "POST"]}

[[redirects]]
  from = "/payu/failure"
  to = "/.netlify/functions/payu-callback-handler"
  status = 200
  force = true
  conditions = {Method = ["GET", "POST"]}

[[redirects]]
  from = "/payu/cancel"
  to = "/.netlify/functions/payu-callback-handler"
  status = 200
  force = true
  conditions = {Method = ["GET", "POST"]}

# PayU webhook handler
[[redirects]]
  from = "/payu/webhook"
  to = "/.netlify/functions/payu-webhook-handler"
  status = 200
  force = true

# Custom domain redirects
[[redirects]]
  from = "https://www.darvigroup.in/*"
  to = "https://darvigroup.in/:splat"
  status = 301
  force = true

# Admin panel redirect
[[redirects]]
  from = "/admin/*"
  to = "/admin/index.html"
  status = 200

# SPA routing (must be last)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200


