import React, { useState } from 'react';
import {
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  TextField,
  Button,
  Box,
  Paper,
  Snackbar,
  Alert,
  Card,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import DirectionsIcon from '@mui/icons-material/Directions';
import Footer from '../components/Footer';
import { useLanguage } from '../contexts/LanguageContext';
import translations from '../translations';
import emailService from '../services/emailService';

interface FormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

const Contact = () => {
  const { language } = useLanguage();
  const t = translations[language as keyof typeof translations].common;

  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    subject: '',
    message: '',
  });
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Send email using our email service
      const result = await emailService.sendEmail({
        ...formData,
        type: 'contact'
      });

      if (result.success) {
        setSnackbar({
          open: true,
          message: language === 'en' ? 'Message sent successfully! Our team will review your message and get back to you soon.' :
                   language === 'kn' ? 'ಸಂದೇಶವನ್ನು ಯಶಸ್ವಿಯಾಗಿ ಕಳುಹಿಸಲಾಗಿದೆ! ನಮ್ಮ ತಂಡವು ನಿಮ್ಮ ಸಂದೇಶವನ್ನು ಪರಿಶೀಲಿಸುತ್ತದೆ ಮತ್ತು ಶೀಘ್ರದಲ್ಲೇ ನಿಮಗೆ ಮರಳಿ ಬರುತ್ತದೆ.' :
                   'संदेश सफलतापूर्वक भेजा गया! हमारी टीम आपके संदेश की समीक्षा करेगी और जल्द ही आपसे संपर्क करेगी।',
          severity: 'success',
        });

        // Reset form
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: '',
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      // Error sending message
      setSnackbar({
        open: true,
        message: language === 'en' ? 'Failed to send message. Please try again later.' :
                 language === 'kn' ? 'ಸಂದೇಶವನ್ನು ಕಳುಹಿಸಲು ವಿಫಲವಾಗಿದೆ. ದಯವಿಟ್ಟು ನಂತರ ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ.' :
                 'संदेश भेजने में विफल। कृपया बाद में पुनः प्रयास करें।',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const contactInfo = [
    {
      icon: <LocationOnIcon sx={{ fontSize: 40 }} />,
      title: t.address,
      content: '#2 Totad building, Arjun Vihar Gokul road Hubli, Karnataka 580030, India',
    },
    {
      icon: <PhoneIcon sx={{ fontSize: 40 }} />,
      title: t.phone,
      content: '+91 99868 90777',
    },
    {
      icon: <EmailIcon sx={{ fontSize: 40 }} />,
      title: t.email,
      content: '<EMAIL>',
    },
    {
      icon: <AccessTimeIcon sx={{ fontSize: 40 }} />,
      title: t.workingHours,
      content: 'Monday - Saturday: 9:00 AM - 6:00 PM',
    },
  ];

  const businessInfo = [
    {
      title: language === 'en' ? 'Company Name' : language === 'kn' ? 'ಕಂಪನಿಯ ಹೆಸರು' : 'कंपनी का नाम',
      content: 'Darvi Group'
    },
    {
      title: language === 'en' ? 'Business Type' : language === 'kn' ? 'ವ್ಯಾಪಾರದ ಪ್ರಕಾರ' : 'व्यापार प्रकार',
      content: language === 'en' ? 'Agricultural Products & Services' : language === 'kn' ? 'ಕೃಷಿ ಉತ್ಪನ್ನಗಳು ಮತ್ತು ಸೇವೆಗಳು' : 'कृषि उत्पाद और सेवाएं'
    },
    {
      title: language === 'en' ? 'Established' : language === 'kn' ? 'ಸ್ಥಾಪನೆ' : 'स्थापना',
      content: '2020'
    },
    {
      title: language === 'en' ? 'Service Area' : language === 'kn' ? 'ಸೇವಾ ಪ್ರದೇಶ' : 'सेवा क्षेत्र',
      content: language === 'en' ? 'Karnataka, India' : language === 'kn' ? 'ಕರ್ನಾಟಕ, ಭಾರತ' : 'कर्नाटक, भारत'
    }
  ];

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  const handleOpenMap = () => {
    // Open the Google Maps location in a new tab
    window.open('https://goo.gl/maps/hCxtTazULeNjFATx9', '_blank', 'noopener,noreferrer');
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <Container maxWidth="lg" sx={{ py: 4, flexGrow: 1 }}>
        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          align="center"
          color="primary"
          sx={{
            fontSize: { xs: '2rem', md: '2.5rem' },
            mb: 2
          }}
        >
          {t.contactUs}
        </Typography>
        <Typography
          variant="subtitle1"
          align="center"
          paragraph
          color="text.secondary"
          sx={{ mb: 4 }}
        >
          {language === 'en' ? 'Get in touch with us for any queries or support' :
           language === 'kn' ? 'ಯಾವುದೇ ಪ್ರಶ್ನೆಗಳು ಅಥವಾ ಬೆಂಬಲಕ್ಕಾಗಿ ನಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸಿ' :
           'किसी भी प्रश्न या सहायता के लिए हमसे संपर्क करें'}
        </Typography>

        <Grid container spacing={isTablet ? 3 : 4} sx={{ mb: 5 }}>
          {/* Contact Information */}
          <Grid item xs={12} md={4}>
            <Paper
              elevation={3}
              sx={{
                p: { xs: 2, md: 3 },
                height: '100%',
                borderRadius: '12px'
              }}
            >
              <Typography variant="h6" gutterBottom color="primary" sx={{ mb: 2 }}>
                {language === 'en' ? 'Contact Information' :
                 language === 'kn' ? 'ಸಂಪರ್ಕ ಮಾಹಿತಿ' :
                 'संपर्क जानकारी'}
              </Typography>
              {contactInfo.map((info, index) => (
                <Box key={index} sx={{ mb: 3 }}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <Box color="primary.main" mr={1}>
                      {info.icon}
                    </Box>
                    <Typography variant="h6" sx={{ fontSize: { xs: '1.1rem', md: '1.25rem' } }}>
                      {info.title}
                    </Typography>
                  </Box>
                  <Typography
                    variant="body1"
                    color="text.secondary"
                    sx={{
                      fontSize: { xs: '0.9rem', md: '1rem' },
                      pl: { xs: 0, sm: 6 }
                    }}
                  >
                    {info.content}
                  </Typography>
                </Box>
              ))}

              {/* Business Information */}
              <Box sx={{ mt: 4, pt: 3, borderTop: '1px solid #e0e0e0' }}>
                <Typography variant="h6" gutterBottom color="primary" sx={{ mb: 2 }}>
                  {language === 'en' ? 'Business Information' :
                   language === 'kn' ? 'ವ್ಯಾಪಾರ ಮಾಹಿತಿ' :
                   'व्यापार जानकारी'}
                </Typography>
                {businessInfo.map((info, index) => (
                  <Box key={index} sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.primary" sx={{ fontWeight: 'bold' }}>
                      {info.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {info.content}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Paper>
          </Grid>

          {/* Contact Form */}
          <Grid item xs={12} md={8}>
            <Paper
              elevation={3}
              sx={{
                p: { xs: 2, md: 3 },
                borderRadius: '12px'
              }}
            >
              <Typography variant="h6" gutterBottom color="primary" sx={{ mb: 2 }}>
                {language === 'en' ? 'Send us a Message' :
                 language === 'kn' ? 'ನಮಗೆ ಸಂದೇಶವನ್ನು ಕಳುಹಿಸಿ' :
                 'हमें संदेश भेजें'}
              </Typography>
              <form onSubmit={handleSubmit}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      required
                      fullWidth
                      label={language === 'en' ? 'Name' : language === 'kn' ? 'ಹೆಸರು' : 'नाम'}
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      variant="outlined"
                      size={isMobile ? "small" : "medium"}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      required
                      fullWidth
                      label={language === 'en' ? 'Email' : language === 'kn' ? 'ಇಮೇಲ್' : 'ईमेल'}
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      variant="outlined"
                      size={isMobile ? "small" : "medium"}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      required
                      fullWidth
                      label={language === 'en' ? 'Subject' : language === 'kn' ? 'ವಿಷಯ' : 'विषय'}
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      variant="outlined"
                      size={isMobile ? "small" : "medium"}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      required
                      fullWidth
                      label={language === 'en' ? 'Message' : language === 'kn' ? 'ಸಂದೇಶ' : 'संदेश'}
                      name="message"
                      multiline
                      rows={isMobile ? 3 : 4}
                      value={formData.message}
                      onChange={handleChange}
                      variant="outlined"
                      size={isMobile ? "small" : "medium"}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      size={isMobile ? "medium" : "large"}
                      fullWidth
                      disabled={loading}
                      sx={{
                        py: { xs: 1, md: 1.5 },
                        mt: 1
                      }}
                    >
                      {loading ?
                        (language === 'en' ? 'Sending...' :
                         language === 'kn' ? 'ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ...' :
                         'भेज रहा है...') :
                        (language === 'en' ? 'Send Message' :
                         language === 'kn' ? 'ಸಂದೇಶ ಕಳುಹಿಸಿ' :
                         'संदेश भेजें')}
                    </Button>
                  </Grid>
                </Grid>
              </form>
            </Paper>
          </Grid>
        </Grid>

        {/* Map Section with Google Maps iframe */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h5"
            component="h2"
            align="center"
            color="primary"
            sx={{
              mb: 3,
              fontSize: { xs: '1.5rem', md: '1.75rem' }
            }}
          >
            {t.ourLocation}
          </Typography>
          <Card
            elevation={4}
            sx={{
              borderRadius: '12px',
              overflow: 'hidden',
              position: 'relative',
            }}
          >
            {/* Google Maps iframe */}
            <Box
              sx={{
                width: '100%',
                height: isMobile ? 250 : 400,
                position: 'relative',
              }}
            >
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3847.9391243261096!2d74.4972!3d15.3568!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTXCsDIxJzI0LjUiTiA3NMKwMjknNDkuOSJF!5e0!3m2!1sen!2sin!4v1623456789012!5m2!1sen!2sin"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen={false}
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Darvi Group Location"
              />
            </Box>

            {/* Overlay with location info and directions button */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                bgcolor: 'rgba(0,0,0,0.7)',
                color: 'white',
                p: { xs: 1.5, md: 2 },
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                alignItems: { xs: 'flex-start', sm: 'center' },
                justifyContent: { xs: 'center', sm: 'space-between' },
                gap: { xs: 1, sm: 0 }
              }}
            >
              <Typography
                variant="body1"
                component="div"
                sx={{
                  fontWeight: 500,
                  fontSize: { xs: '0.9rem', md: '1rem' }
                }}
              >
                Darvi Group, #2 Totad building, Arjun Vihar Gokul road Hubli
              </Typography>
              <Button
                variant="contained"
                size="small"
                startIcon={<DirectionsIcon />}
                onClick={handleOpenMap}
                sx={{
                  bgcolor: '#1B4C35',
                  '&:hover': {
                    bgcolor: '#4CAF50',
                  },
                  borderRadius: '50px',
                  px: 2,
                }}
              >
                {t.getDirections}
              </Button>
            </Box>
          </Card>
        </Box>

        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
        >
          <Alert
            onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>

      <Footer />
    </Box>
  );
};

export default Contact;
