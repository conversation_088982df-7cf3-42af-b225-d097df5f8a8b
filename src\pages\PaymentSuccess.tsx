/**
 * Payment Success Page
 * Displays payment success information and receipt download options
 */

import React, { useEffect, useState, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  Card,
  CardContent,
  Divider,
  Grid,
  Chip,
  CircularProgress,
  Backdrop,
  LinearProgress
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Home as HomeIcon,
  Email as EmailIcon,
  CreditCard as CardIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';

import { useLanguage } from '../contexts/LanguageContext';
import ReceiptGenerator, { ReceiptGeneratorRef } from '../components/ReceiptGenerator';
import receiptDataExtractor from '../utils/receiptDataExtractor';
import { ReceiptData } from '../services/receiptService';
import googleSheetsService from '../services/googleSheets';


const PaymentSuccess: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { language } = useLanguage();
  const receiptGeneratorRef = useRef<ReceiptGeneratorRef>(null);

  const [receiptData, setReceiptData] = useState<ReceiptData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submittingToSheets, setSubmittingToSheets] = useState(false);
  const [sheetsSubmitted, setSheetsSubmitted] = useState(false);
  const [autoDownloadTriggered, setAutoDownloadTriggered] = useState(false);

  // Translations
  const translations = {
    en: {
      title: 'Payment Successful!',
      subtitle: 'Your registration has been completed successfully',
      thankYou: 'Thank you for choosing Darvi Group',
      paymentDetails: 'Payment Details',
      customerInfo: 'Customer Information',
      transactionInfo: 'Transaction Information',
      downloadReceipt: 'Download Receipt',
      backToHome: 'Back to Home',
      contactSupport: 'Contact Support',
      amount: 'Amount Paid',
      transactionId: 'Transaction ID',
      paymentId: 'Payment ID',
      paymentMethod: 'Payment Method',
      paymentDate: 'Payment Date',
      clientId: 'Client ID',
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      status: 'Status',
      successful: 'Successful',
      registrationComplete: 'Your farmer registration is now complete. You will receive a confirmation email shortly.',
      nextSteps: 'Next Steps',
      nextStepsDesc: 'Our team will contact you within 24-48 hours to discuss your farming requirements and schedule a consultation.',
      receiptNote: 'Keep your receipt for future reference. You can download it anytime using the button below.',
      supportNote: 'If you have any questions, feel free to contact our support team.',
      loadingReceipt: 'Loading receipt data...',
      errorLoadingReceipt: 'Error loading receipt data. Please contact support.'
    },
    hi: {
      title: 'भुगतान सफल!',
      subtitle: 'आपका पंजीकरण सफलतापूर्वक पूरा हो गया है',
      thankYou: 'दर्वी ग्रुप चुनने के लिए धन्यवाद',
      paymentDetails: 'भुगतान विवरण',
      customerInfo: 'ग्राहक जानकारी',
      transactionInfo: 'लेनदेन जानकारी',
      downloadReceipt: 'रसीद डाउनलोड करें',
      backToHome: 'होम पर वापस जाएं',
      contactSupport: 'सहायता से संपर्क करें',
      amount: 'भुगतान की गई राशि',
      transactionId: 'लेनदेन आईडी',
      paymentId: 'भुगतान आईडी',
      paymentMethod: 'भुगतान विधि',
      paymentDate: 'भुगतान दिनांक',
      clientId: 'क्लाइंट आईडी',
      name: 'नाम',
      email: 'ईमेल',
      phone: 'फोन',
      status: 'स्थिति',
      successful: 'सफल',
      registrationComplete: 'आपका किसान पंजीकरण अब पूरा हो गया है। आपको जल्द ही एक पुष्टिकरण ईमेल प्राप्त होगा।',
      nextSteps: 'अगले कदम',
      nextStepsDesc: 'हमारी टीम आपकी खेती की आवश्यकताओं पर चर्चा करने और परामर्श निर्धारित करने के लिए 24-48 घंटों के भीतर आपसे संपर्क करेगी।',
      receiptNote: 'भविष्य के संदर्भ के लिए अपनी रसीद रखें। आप नीचे दिए गए बटन का उपयोग करके इसे कभी भी डाउनलोड कर सकते हैं।',
      supportNote: 'यदि आपके कोई प्रश्न हैं, तो बेझिझक हमारी सहायता टीम से संपर्क करें।',
      loadingReceipt: 'रसीद डेटा लोड हो रहा है...',
      errorLoadingReceipt: 'रसीद डेटा लोड करने में त्रुटि। कृपया सहायता से संपर्क करें।'
    },
    kn: {
      title: 'ಪಾವತಿ ಯಶಸ್ವಿಯಾಗಿದೆ!',
      subtitle: 'ನಿಮ್ಮ ನೋಂದಣಿ ಯಶಸ್ವಿಯಾಗಿ ಪೂರ್ಣಗೊಂಡಿದೆ',
      thankYou: 'ದರ್ವಿ ಗ್ರೂಪ್ ಅನ್ನು ಆಯ್ಕೆ ಮಾಡಿದ್ದಕ್ಕಾಗಿ ಧನ್ಯವಾದಗಳು',
      paymentDetails: 'ಪಾವತಿ ವಿವರಗಳು',
      customerInfo: 'ಗ್ರಾಹಕ ಮಾಹಿತಿ',
      transactionInfo: 'ವಹಿವಾಟು ಮಾಹಿತಿ',
      downloadReceipt: 'ರಸೀದಿ ಡೌನ್‌ಲೋಡ್ ಮಾಡಿ',
      backToHome: 'ಮುಖ್ಯ ಪುಟಕ್ಕೆ ಹಿಂತಿರುಗಿ',
      contactSupport: 'ಬೆಂಬಲವನ್ನು ಸಂಪರ್ಕಿಸಿ',
      amount: 'ಪಾವತಿಸಿದ ಮೊತ್ತ',
      transactionId: 'ವಹಿವಾಟು ಐಡಿ',
      paymentId: 'ಪಾವತಿ ಐಡಿ',
      paymentMethod: 'ಪಾವತಿ ವಿಧಾನ',
      paymentDate: 'ಪಾವತಿ ದಿನಾಂಕ',
      clientId: 'ಕ್ಲೈಂಟ್ ಐಡಿ',
      name: 'ಹೆಸರು',
      email: 'ಇಮೇಲ್',
      phone: 'ಫೋನ್',
      status: 'ಸ್ಥಿತಿ',
      successful: 'ಯಶಸ್ವಿ',
      registrationComplete: 'ನಿಮ್ಮ ರೈತ ನೋಂದಣಿ ಈಗ ಪೂರ್ಣಗೊಂಡಿದೆ. ನೀವು ಶೀಘ್ರದಲ್ಲೇ ದೃಢೀಕರಣ ಇಮೇಲ್ ಅನ್ನು ಸ್ವೀಕರಿಸುವಿರಿ.',
      nextSteps: 'ಮುಂದಿನ ಹಂತಗಳು',
      nextStepsDesc: 'ನಿಮ್ಮ ಕೃಷಿ ಅವಶ್ಯಕತೆಗಳನ್ನು ಚರ್ಚಿಸಲು ಮತ್ತು ಸಮಾಲೋಚನೆಯನ್ನು ನಿಗದಿಪಡಿಸಲು ನಮ್ಮ ತಂಡವು 24-48 ಗಂಟೆಗಳಲ್ಲಿ ನಿಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸುತ್ತದೆ.',
      receiptNote: 'ಭವಿಷ್ಯದ ಉಲ್ಲೇಖಕ್ಕಾಗಿ ನಿಮ್ಮ ರಸೀದಿಯನ್ನು ಇರಿಸಿಕೊಳ್ಳಿ. ಕೆಳಗಿನ ಬಟನ್ ಬಳಸಿ ನೀವು ಯಾವಾಗ ವೇಣಾ ಅದನ್ನು ಡೌನ್‌ಲೋಡ್ ಮಾಡಬಹುದು.',
      supportNote: 'ನಿಮಗೆ ಯಾವುದೇ ಪ್ರಶ್ನೆಗಳಿದ್ದರೆ, ನಮ್ಮ ಬೆಂಬಲ ತಂಡವನ್ನು ಸಂಪರ್ಕಿಸಲು ಹಿಂಜರಿಯಬೇಡಿ.',
      loadingReceipt: 'ರಸೀದಿ ಡೇಟಾ ಲೋಡ್ ಆಗುತ್ತಿದೆ...',
      errorLoadingReceipt: 'ರಸೀದಿ ಡೇಟಾ ಲೋಡ್ ಮಾಡುವಲ್ಲಿ ದೋಷ. ದಯವಿಟ್ಟು ಬೆಂಬಲವನ್ನು ಸಂಪರ್ಕಿಸಿ.'
    }
  };

  const t = translations[language as keyof typeof translations];

  useEffect(() => {
    const processPaymentSuccess = async () => {
      try {
        setLoading(true);

        // Get payment details from URL parameters
        const txnid = searchParams.get('txnid');
        const mihpayid = searchParams.get('mihpayid');
        const amount = searchParams.get('amount');
        const mode = searchParams.get('mode');
        const status = searchParams.get('status');

        // Get additional PayU parameters that might be available
        const firstname = searchParams.get('firstname');
        const email = searchParams.get('email');
        const phone = searchParams.get('phone');
        const timestamp = searchParams.get('timestamp');
        const verified = searchParams.get('verified');
        const hashVerified = searchParams.get('hashVerified');
        const bank_ref_num = searchParams.get('bank_ref_num');
        const bankcode = searchParams.get('bankcode');

        if (!txnid) {
          setError('No payment information found');
          setLoading(false);
          return;
        }

        // Get stored form data
        const storedFormData = localStorage.getItem('payuCheckoutData');
        if (!storedFormData) {
          setError('No form data found. Please contact support.');
          setLoading(false);
          return;
        }

        const formData = JSON.parse(storedFormData);

        // Extract receipt data with all available PayU parameters
        const paymentData = {
          txnid,
          mihpayid,
          amount,
          mode,
          status: status || 'success',
          firstname,
          email,
          phone,
          timestamp,
          bank_ref_num,
          bankcode,
          paymentMethod: mode,
          paymentGateway: 'PayU',
          // Additional PayU parameters that might be available
          pg_type: searchParams.get('pg_type'),
          bank_name: searchParams.get('bank_name'),
          cardnum: searchParams.get('cardnum'),
          cardhash: searchParams.get('cardhash'),
          upi_va: searchParams.get('upi_va'),
          addedon: searchParams.get('addedon'),
          error: searchParams.get('error'),
          error_Message: searchParams.get('error_Message'),
          discount: searchParams.get('discount'),
          net_amount_debit: searchParams.get('net_amount_debit')
        };

        const transactionDetails = {
          hash_verified: hashVerified === 'true' || verified === 'true',
          timestamp: timestamp || new Date().toISOString()
        };

        const extractedReceiptData = receiptDataExtractor.extractReceiptData(
          paymentData,
          formData,
          transactionDetails
        );

        setReceiptData(extractedReceiptData);
        setLoading(false);

        // Now submit to Google Sheets with loading state
        setSubmittingToSheets(true);

        // Prepare complete form data for Google Sheets
        const completeFormData = {
          ...formData,
          paymentCompleted: true,
          paymentAmount: `₹${extractedReceiptData.totalAmount}`,
          paymentMethod: mode || 'PayU',
          paymentTimestamp: new Date().toISOString(),
          transactionId: txnid,
          mihpayid: mihpayid,
          clientId: extractedReceiptData.clientId,
          bankReferenceNumber: bank_ref_num,
          bankCode: bankcode
        };

        // Submit to Google Sheets
        const submissionResult = await googleSheetsService.submitForm(completeFormData);

        if (submissionResult.success) {
          setSheetsSubmitted(true);

          // Store receipt data for potential future use
          localStorage.setItem('lastReceiptData', JSON.stringify(extractedReceiptData));

          // Clean up stored checkout data
          localStorage.removeItem('payuCheckoutData');
          localStorage.removeItem('payuTransaction');
        } else {
          // Even if Google Sheets fails, we still show success since payment was successful
          setSheetsSubmitted(true);
          // Google Sheets submission failed, but payment was successful
        }

      } catch (err) {
        // Error processing payment success
        setError('Payment successful but there was an error processing your registration. Please contact support.');
        setSheetsSubmitted(true); // Still show success since payment worked
      } finally {
        setSubmittingToSheets(false);
      }
    };

    processPaymentSuccess();
  }, [searchParams]);

  // Auto-download receipt after Google Sheets submission is complete
  useEffect(() => {
    if (sheetsSubmitted && receiptData && !autoDownloadTriggered && receiptGeneratorRef.current) {
      const timer = setTimeout(async () => {
        setAutoDownloadTriggered(true);
        // Trigger receipt download using ref
        try {
          await receiptGeneratorRef.current?.downloadPDF();
        } catch (error) {
          // Auto-download failed silently
        }
      }, 3000); // 3 seconds delay

      return () => clearTimeout(timer);
    }
  }, [sheetsSubmitted, receiptData, autoDownloadTriggered]);

  const handleBackToHome = () => {
    // Clean up stored data
    localStorage.removeItem('lastReceiptData');
    localStorage.removeItem('payuCheckoutData');
    localStorage.removeItem('payuTransaction');
    navigate('/');
  };

  const handleContactSupport = () => {
    window.location.href = 'tel:+919986890777';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
          <CircularProgress size={60} />
          <Typography variant="h6">{t.loadingReceipt}</Typography>
        </Box>
      </Container>
    );
  }

  // Show loading state for Google Sheets submission
  if (submittingToSheets) {
    return (
      <>
        <Backdrop
          open={true}
          sx={{
            color: '#fff',
            zIndex: (theme) => theme.zIndex.drawer + 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: { xs: 2, sm: 4 }
          }}
        >
          <Box
            sx={{
              textAlign: 'center',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
              maxWidth: { xs: '90%', sm: '400px' },
              px: { xs: 2, sm: 0 }
            }}
          >
            <CircularProgress
              size={60}
              sx={{
                mb: { xs: 2, sm: 3 },
                color: '#4CAF50'
              }}
            />
            <Typography
              variant="h5"
              gutterBottom
              sx={{
                fontSize: { xs: '1.25rem', sm: '1.5rem' },
                fontWeight: 600,
                mb: { xs: 1, sm: 2 },
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexWrap: 'wrap'
              }}
            >
              <CloudUploadIcon
                sx={{
                  mr: 1,
                  fontSize: { xs: '1.5rem', sm: '2rem' },
                  color: '#4CAF50'
                }}
              />
              Submitting to form...
            </Typography>
            <Typography
              variant="body1"
              sx={{
                opacity: 0.9,
                fontSize: { xs: '0.875rem', sm: '1rem' },
                mb: { xs: 2, sm: 3 },
                textAlign: 'center'
              }}
            >
              Please wait while we process your registration
            </Typography>
            <LinearProgress
              sx={{
                mt: 1,
                width: { xs: '100%', sm: '300px' },
                height: { xs: 6, sm: 4 },
                borderRadius: 3,
                backgroundColor: 'rgba(255,255,255,0.3)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#4CAF50'
                }
              }}
            />
          </Box>
        </Backdrop>
      </>
    );
  }

  if (error || !receiptData) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error || t.errorLoadingReceipt}
        </Alert>
        <Box display="flex" justifyContent="center">
          <Button
            variant="contained"
            startIcon={<HomeIcon />}
            onClick={handleBackToHome}
          >
            {t.backToHome}
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{
      py: { xs: 2, sm: 3, md: 4 },
      px: { xs: 1, sm: 2, md: 3 }
    }}>
      {/* Success Header */}
      <Paper elevation={3} sx={{
        p: { xs: 2, sm: 3, md: 4 },
        mb: { xs: 2, sm: 3 },
        textAlign: 'center',
        bgcolor: 'success.light',
        color: 'success.contrastText',
        borderRadius: { xs: '12px', sm: '16px' }
      }}>
        <CheckCircleIcon sx={{
          fontSize: { xs: 60, sm: 70, md: 80 },
          mb: { xs: 1, sm: 2 }
        }} />
        <Typography
          variant="h3"
          component="h1"
          gutterBottom
          fontWeight="bold"
          sx={{
            fontSize: { xs: '1.75rem', sm: '2.5rem', md: '3rem' },
            lineHeight: 1.2
          }}
        >
          {t.title}
        </Typography>
        <Typography
          variant="h6"
          sx={{
            opacity: 0.9,
            fontSize: { xs: '1rem', sm: '1.125rem', md: '1.25rem' },
            lineHeight: 1.4
          }}
        >
          {t.subtitle}
        </Typography>
        <Chip
          label={t.successful}
          color="success"
          variant="filled"
          sx={{
            mt: { xs: 1.5, sm: 2 },
            fontWeight: 'bold',
            fontSize: { xs: '0.875rem', sm: '1rem' },
            height: { xs: '32px', sm: '36px' }
          }}
        />
      </Paper>

      {/* Registration Complete Message */}
      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="body1" gutterBottom>
          <strong>{t.thankYou}!</strong>
        </Typography>
        <Typography variant="body2">
          {t.registrationComplete}
        </Typography>
      </Alert>

      {/* Auto-download notification */}
      {sheetsSubmitted && !autoDownloadTriggered && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            📄 Your receipt will automatically download in a few seconds...
          </Typography>
        </Alert>
      )}

      {/* Download completed notification */}
      {autoDownloadTriggered && (
        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="body2">
            ✅ Receipt downloaded successfully! Check your downloads folder.
          </Typography>
        </Alert>
      )}

      {/* Payment Details */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CardIcon />
                {t.paymentDetails}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">{t.amount}:</Typography>
                <Typography variant="body2" fontWeight="bold">{formatCurrency(receiptData.totalAmount)}</Typography>
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">{t.paymentMethod}:</Typography>
                <Typography variant="body2">{receiptData.paymentMethod}</Typography>
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">{t.paymentDate}:</Typography>
                <Typography variant="body2">{formatDate(receiptData.paymentDate)}</Typography>
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" color="text.secondary">{t.status}:</Typography>
                <Chip label={receiptData.status} color="success" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                {t.transactionInfo}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">{t.clientId}:</Typography>
                <Typography variant="body2" fontFamily="monospace" fontSize="0.8rem">{receiptData.clientId}</Typography>
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">{t.transactionId}:</Typography>
                <Typography variant="body2" fontFamily="monospace" fontSize="0.8rem">{receiptData.transactionId}</Typography>
              </Box>
              
              {receiptData.paymentId && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">{t.paymentId}:</Typography>
                  <Typography variant="body2" fontFamily="monospace" fontSize="0.8rem">{receiptData.paymentId}</Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Customer Information */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="primary">
            {t.customerInfo}
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography variant="body2" color="text.secondary">{t.name}:</Typography>
              <Typography variant="body1">{receiptData.customerName}</Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography variant="body2" color="text.secondary">{t.email}:</Typography>
              <Typography variant="body1">{receiptData.customerEmail}</Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography variant="body2" color="text.secondary">{t.phone}:</Typography>
              <Typography variant="body1">{receiptData.customerPhone}</Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body1" gutterBottom>
          <strong>{t.nextSteps}</strong>
        </Typography>
        <Typography variant="body2" gutterBottom>
          {t.nextStepsDesc}
        </Typography>
        <Typography variant="body2">
          {t.receiptNote}
        </Typography>
      </Alert>

      {/* Action Buttons */}
      <Box
        display="flex"
        flexDirection={{ xs: 'column', sm: 'row' }}
        gap={{ xs: 1.5, sm: 2 }}
        justifyContent="center"
        px={{ xs: 1, sm: 0 }}
      >
        <ReceiptGenerator
          ref={receiptGeneratorRef}
          receiptData={receiptData}
          onGenerated={(_blob) => {
            // Receipt generated successfully
          }}
          onError={(_error) => {
            alert('Failed to generate receipt. Please try again.');
          }}
        />

        <Button
          variant="outlined"
          startIcon={<EmailIcon />}
          onClick={handleContactSupport}
          size="large"
          sx={{
            width: { xs: '100%', sm: 'auto' },
            minHeight: { xs: '48px', sm: '44px' },
            fontSize: { xs: '1rem', sm: '0.875rem' },
            borderRadius: { xs: '12px', sm: '50px' },
            borderWidth: '2px',
            '&:hover': {
              borderWidth: '2px',
              transform: 'translateY(-3px)',
              boxShadow: '0 6px 20px rgba(0,0,0,0.1)',
            }
          }}
        >
          {t.contactSupport}
        </Button>

        <Button
          variant="contained"
          startIcon={<HomeIcon />}
          onClick={handleBackToHome}
          size="large"
          sx={{
            width: { xs: '100%', sm: 'auto' },
            minHeight: { xs: '48px', sm: '44px' },
            fontSize: { xs: '1rem', sm: '0.875rem' },
            fontWeight: 600,
            borderRadius: { xs: '12px', sm: '50px' },
            boxShadow: '0 4px 14px 0 rgba(0,0,0,0.1)',
            '&:hover': {
              boxShadow: '0 6px 20px rgba(0,0,0,0.2)',
              transform: 'translateY(-3px)',
            }
          }}
        >
          {t.backToHome}
        </Button>
      </Box>

      {/* Support Note */}
      <Box mt={4} textAlign="center">
        <Typography variant="body2" color="text.secondary">
          {t.supportNote}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          📞 +91 99868 90777 | 🌐 www.darvigroup.in
        </Typography>
      </Box>
    </Container>
  );
};

export default PaymentSuccess;
