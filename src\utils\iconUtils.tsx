import React from 'react';

// Simple icon replacement utility using emojis
export const IconComponent: React.FC<{ name: string; size?: number; color?: string }> = ({ 
  name, 
  size = 24, 
  color = '#1976d2' 
}) => {
  const iconMap: { [key: string]: string } = {
    // Navigation
    'menu': '☰',
    'home': '🏠',
    'info': 'ℹ️',
    'close': '✕',
    
    // Contact
    'phone': '📞',
    'email': '📧',
    'location': '📍',
    'time': '🕐',
    'directions': '🧭',
    
    // Social
    'facebook': '📘',
    'twitter': '🐦',
    'instagram': '📷',
    'linkedin': '💼',
    'whatsapp': '💬',
    
    // Services
    'agriculture': '🌱',
    'science': '🔬',
    'support': '🛠️',
    'trending': '📈',
    'school': '🎓',
    'work': '💼',
    'star': '⭐',
    'nature': '🌿',
    'public': '🌍',
    'router': '📡',
    'chart': '📊',
    'lightbulb': '💡',
    'groups': '👥',
    'business': '🏢',
    'registration': '📝',
    'language': '🌐',
    'privacy': '🔒',
    'money': '💰',
    'gavel': '⚖️',
    
    // Payment
    'check': '✅',
    'cancel': '❌',
    'success': '✅',
    'error': '❌',
    
    // Chat
    'send': '📤',
    'robot': '🤖',
    'person': '👤',
    
    // Navigation arrows
    'arrow-back': '◀',
    'arrow-forward': '▶',
    'arrow-up': '▲',
    'arrow-down': '▼',
    'expand': '⌄',
    
    // Misc
    'storefront': '🏪',
    'record': '⚫',
    'default': '●'
  };

  const emoji = iconMap[name] || iconMap['default'];
  
  return (
    <span 
      style={{ 
        fontSize: `${size}px`, 
        color, 
        display: 'inline-block',
        lineHeight: 1
      }}
    >
      {emoji}
    </span>
  );
};

export default IconComponent;
